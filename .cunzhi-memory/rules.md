# 开发规范和规则

- 项目已成功迁移到Tailwind CSS 4 + Vite插件配置，使用@import "tailwindcss"语法，animejs已升级到4.0.2版本
- 移动端优先布局规范：容器间距px-3 py-4，输入框高度h-12，按钮最小44px触控区域，网格布局grid-cols-1 sm:grid-cols-2，文字大小text-base sm:text-lg响应式
- DaisyUI 5最佳实践：使用shadow-sm替代shadow-2xl，移除input-bordered（默认有边框），页面背景使用bg-base-200，避免重复标题
- CSS样式冲突解决方案：移除自定义input、steps、progress样式，使用DaisyUI原生样式，通过input-ghost类移除边框，避免自定义CSS覆盖DaisyUI组件
- 进度条和边框完全修复：使用DaisyUI原生progress元素替代自定义HTML，单位标签使用btn-ghost移除边框，彻底清理所有自定义btn样式避免DaisyUI冲突
- Setup页面优化完成：移除冗余进度条，实现全屏布局，目标设置字段顺序调整为天数在前体重在后，添加智能推荐体重功能，统一单位标签样式，优化布局间距
- Setup页面交互优化完成：修复推荐按钮高度匹配，集成活动水平到推荐算法，应用2024年现代化配色方案（emerald-indigo渐变），推荐按钮需要目标天数+活动水平才启用
- Setup页面最终优化完成：统一emerald配色边框系统，调整交互流程为目标天数→活动水平→目标体重，重新设计基本信息页面为卡片式布局，应用渐变标题和现代化视觉效果
- Setup页面移动端优先重新设计完成：以390x844为标准，应用2025年indigo-purple配色方案，修复输入框黑色边框问题，移除max-w限制实现全屏布局，垂直布局优化触控体验
- Setup页面推荐算法与确认页面优化完成：修复推荐算法验证问题，放宽体重限制至20-500kg，优化确认信息页面为移动端卡片式布局，推荐算法与验证逻辑保持一致
- Setup页面最终UI优化完成：导航按钮专业化设计，桌面端充分利用屏幕宽度（max-w-4xl/5xl/6xl），活动水平默认选择中度活动，确认页面4列网格布局，响应式间距优化
- Setup页面最终优化完成：移除活动水平默认值，修复桌面端目标天数宽度对齐（max-w-xl），集成Anime.js动画效果包括步骤切换、按钮点击、输入框聚焦、错误提示、推荐结果显示等动画
- Setup页面宽度对齐优化完成：目标天数输入框、活动水平按钮、目标体重输入框统一使用max-w-md lg:max-w-lg xl:max-w-xl宽度限制，实现完美视觉对齐
- 移动端充满屏幕布局优化：外层容器px-0 py-0移除边距，主内容卡片rounded-none shadow-none border-0移除装饰，各区域使用px-4 sm:px-0确保移动端内容有适当间距但充满屏幕
- Setup页面姓名输入框宽度对齐修复：移除max-w限制使其与性别按钮组宽度匹配；Dashboard页面UI全面现代化：使用DaisyUI 5.0.46组件，渐变背景，毛玻璃效果，现代化卡片设计，emoji图标，stats组件，badge组件，现代化按钮设计
- Dashboard页面移动端完全重构完成：基于Anime.js v4动画系统，移动端优先设计，单列布局，44px最小触控区域，毛玻璃效果，现代化卡片设计，响应式间距，完美的390px移动端适配
- Dashboard页面UI细节优化完成：统一三餐分配样式为badge-primary，所有数值格式化为整数（formatWeight、formatNutrient使用Math.round），移除冗余头部元素，创建现代化实时日期时间显示组件
- Dashboard页面卡片内部布局优化与UI美化重构完成：今日卡路里炫酷橙红渐变设计，营养详情绿色系数据可视化，三餐分配彩色进度条设计，代谢信息玫瑰色优化布局，个人档案天蓝绿色现代化设计，所有数值显示整数
- Dashboard页面UI细节修复与数据优化完成：简化个人档案移除进度条，统一时间日期紫色风格，营养建议琥珀色设计，修复营养计划卡路里限额，所有进度条基于真实数据计算，Math.round确保整数显示
- Dashboard页面环形图重叠显示修复与营养建议UI重构完成：移除环形图上重叠的完成度百分比显示，营养建议组件完全重构为现代化渐变卡片设计，emoji图标系统，毛玻璃效果，与整体风格统一
- Dashboard页面环形图移除与卡片设计统一优化完成：移除今日卡路里环形图，恢复个人档案👤图标，移除营养建议和每日卡路里限额的白色毛玻璃包装，统一卡片设计风格，直接展示渐变背景
