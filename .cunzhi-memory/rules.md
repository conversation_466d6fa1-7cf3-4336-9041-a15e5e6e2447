# 开发规范和规则

- 项目已成功迁移到Tailwind CSS 4 + Vite插件配置，使用@import "tailwindcss"语法，animejs已升级到4.0.2版本
- 移动端优先布局规范：容器间距px-3 py-4，输入框高度h-12，按钮最小44px触控区域，网格布局grid-cols-1 sm:grid-cols-2，文字大小text-base sm:text-lg响应式
- DaisyUI 5最佳实践：使用shadow-sm替代shadow-2xl，移除input-bordered（默认有边框），页面背景使用bg-base-200，避免重复标题
- CSS样式冲突解决方案：移除自定义input、steps、progress样式，使用DaisyUI原生样式，通过input-ghost类移除边框，避免自定义CSS覆盖DaisyUI组件
- 进度条和边框完全修复：使用DaisyUI原生progress元素替代自定义HTML，单位标签使用btn-ghost移除边框，彻底清理所有自定义btn样式避免DaisyUI冲突
- Setup页面优化完成：移除冗余进度条，实现全屏布局，目标设置字段顺序调整为天数在前体重在后，添加智能推荐体重功能，统一单位标签样式，优化布局间距
