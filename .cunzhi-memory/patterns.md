# 常用模式和最佳实践

- 移动端优化最佳实践：1.使用响应式文字hidden sm:inline显示不同内容 2.触控区域最小44px 3.网格布局grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 4.间距space-y-4 sm:space-y-6渐进式增加
- 响应式宽度对齐最佳实践：使用统一的max-w断点类名（max-w-md lg:max-w-lg xl:max-w-xl）确保不同元素在各屏幕尺寸下保持一致的视觉宽度，避免w-full无限制导致的对齐问题
- 2025年现代化UI设计最佳实践：bg-white/80 backdrop-blur-sm毛玻璃效果，shadow-xl阴影，border border-white/20边框，渐变背景from-indigo-50 via-white to-purple-50，emoji图标增强视觉效果，stats组件展示数据，badge组件标签，btn-lg h-16大按钮设计
- Anime.js v4移动端动画最佳实践：createScope创建动画作用域，支持媒体查询检测，prefers-reduced-motion无障碍支持，stagger错开动画，页面加载动画，卡片hover动画，按钮点击反馈动画，60fps性能优化
- 实时日期时间组件最佳实践：useState管理当前时间，setInterval每秒更新，toLocaleDateString中文格式化，font-mono等宽字体，time-digit类名配合Anime.js动画，毛玻璃卡片设计，响应式文字大小
- 炫酷UI设计最佳实践：渐变背景装饰圆形元素，毛玻璃backdrop-blur-sm效果，进度条可视化数据，emoji图标圆形容器，bg-clip-text渐变文字，相对定位装饰元素，shadow-lg阴影层次，border渐变边框，Math.round整数显示
