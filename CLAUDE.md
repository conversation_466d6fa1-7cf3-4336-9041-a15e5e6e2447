# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Commands
- `npm run dev` - Start development server (Vite on localhost:5173)
- `npm run build` - Build for production (TypeScript compilation + Vite build)
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint code quality checks
- `npm run lint:fix` - Fix ESLint issues automatically
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

### Testing
- `npm run test` - Run tests with Vitest
- `npm run test:ui` - Run tests with UI interface
- `npm run test:coverage` - Run tests with coverage report

### Test Pages
- `/camera-test` - Camera functionality testing
- `/ai-test` - AI food recognition testing

## Architecture Overview

### Domain-Driven Design Structure
The codebase follows a clean architecture with domain-driven design:

```
src/
├── app/                    # Application layer (pages, routing)
├── domains/               # Business domains (user, nutrition, food, analytics)
├── shared/               # Shared utilities and components
└── infrastructure/      # External services (storage, AI)
```

### Key Architectural Patterns

**Domain Separation**: Four main business domains:
- `user/` - User profile management and BMR calculations
- `nutrition/` - Nutrition tracking and analysis
- `food/` - Food recognition and entry management
- `analytics/` - Data visualization and calendar features

**Component Hierarchy**: Atomic design with three levels:
- `atoms/` - Basic UI components (Button, Input, Card, etc.)
- `molecules/` - Composite components (FormField, Modal, Camera, etc.)
- Page-level components in `app/pages/`

**State Management**: Zustand stores in each domain for localized state management with persistence middleware.

**Data Layer**: Repository pattern with IndexedDB storage:
- `BaseRepository.ts` - Abstract base class
- Domain-specific repositories (UserRepository, FoodRepository)
- Database manager with singleton pattern

### Routing & Navigation
Protected route system based on profile completion status:
- Root redirects to `/setup` or `/dashboard` based on profile state
- Route guards prevent access to main app without completed profile
- Test pages accessible when profile is complete

### Key Technologies & Integrations

**AI Integration**: Google Gemini 2.0 Flash for food recognition via structured prompts in `infrastructure/ai/geminiService.ts`

**PWA Configuration**: Vite PWA plugin with service worker, offline caching, and app manifest for native-like experience

**Mobile-First Design**: Tailwind CSS with responsive breakpoints, camera integration using MediaDevices API

**Data Persistence**: IndexedDB with Repository pattern, automatic initialization, and export/import capabilities

## Important Implementation Details

### Environment Variables
- `VITE_GEMINI_API_KEY` - Required for AI food recognition functionality

### Path Aliases
- `@/` - Maps to `src/` directory for clean imports

### Build Optimization
- Manual chunks configuration for vendor, UI, charts, and utilities
- PWA caching strategy for Gemini API calls

### Type Safety
Comprehensive TypeScript types in `shared/types/` covering all domains with strict mode enabled.

### Database Schema
Four main stores: userProfiles, foodEntries, dailySummaries, settings with automatic initialization on app startup.