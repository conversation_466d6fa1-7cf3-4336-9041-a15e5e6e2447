{"permissions": {"allow": ["Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(grep:*)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(npm uninstall:*)", "mcp__mcp-feedback-enhanced__interactive_feedback", "mcp__memory__search_nodes", "mcp__sequential-thinking__read_file", "mcp__sequential-thinking__list_directory", "mcp__shrimp-task-manager__research_mode", "mcp__shrimp-task-manager__plan_task", "mcp__shrimp-task-manager__analyze_task", "mcp__sequential-thinking__search_code", "mcp__shrimp-task-manager__reflect_task", "mcp__shrimp-task-manager__split_tasks", "mcp__shrimp-task-manager__execute_task", "mcp__shrimp-task-manager__list_tasks", "mcp__shrimp-task-manager__query_task", "Bash(timeout 10s npm run dev)", "Bash(npm run lint)", "mcp______ji", "mcp______zhi", "mcp__desktop-commander__search_code", "mcp__desktop-commander__read_file", "Bash(tsc --noEmit)", "Bash(npx tsc:*)"], "deny": []}}