import { BMRCalculationParams, BMRCalculationResult } from '@/shared/types';

/**
 * 基于Mifflin-St Jeor公式计算BMR
 * 男性：BMR = 10 × 体重(kg) + 6.25 × 身高(cm) - 5 × 年龄 + 5
 * 女性：BMR = 10 × 体重(kg) + 6.25 × 身高(cm) - 5 × 年龄 - 161
 */
export function calculateBMR(params: BMRCalculationParams): number {
  const { weight, height, age, gender } = params;
  
  const baseBMR = 10 * weight + 6.25 * height - 5 * age;
  return gender === 'male' ? baseBMR + 5 : baseBMR - 161;
}

/**
 * 活动水平系数
 */
export const ACTIVITY_MULTIPLIERS = {
  sedentary: 1.2,      // 久坐不动
  light: 1.375,        // 轻度活动
  moderate: 1.55,      // 中度活动
  active: 1.725,       // 高度活动
  veryActive: 1.9      // 极高活动
} as const;

/**
 * 计算TDEE (Total Daily Energy Expenditure)
 */
export function calculateTDEE(bmr: number, activityLevel: keyof typeof ACTIVITY_MULTIPLIERS): number {
  return bmr * ACTIVITY_MULTIPLIERS[activityLevel];
}

/**
 * 计算每日卡路里限额（基于减重目标）
 */
export function calculateDailyCalorieLimit(
  tdee: number,
  targetWeightLoss: number, // kg
  targetDays: number
): number {
  // 1kg脂肪 ≈ 7700卡路里
  const totalCalorieDeficit = targetWeightLoss * 7700;
  const dailyDeficit = totalCalorieDeficit / targetDays;
  
  // 确保每日卡路里不低于BMR的80%（安全下限）
  const minCalories = tdee * 0.8;
  const targetCalories = tdee - dailyDeficit;
  
  return Math.max(targetCalories, minCalories);
}

/**
 * 计算三餐卡路里分配
 */
export function calculateMealCalories(
  dailyLimit: number,
  ratios: { breakfast: number; lunch: number; dinner: number }
): { breakfast: number; lunch: number; dinner: number } {
  return {
    breakfast: Math.round(dailyLimit * ratios.breakfast),
    lunch: Math.round(dailyLimit * ratios.lunch),
    dinner: Math.round(dailyLimit * ratios.dinner)
  };
}

/**
 * 完整的BMR和卡路里计算
 */
export function calculateNutritionPlan(
  params: BMRCalculationParams & {
    targetWeight: number;
    targetDays: number;
    activityLevel: keyof typeof ACTIVITY_MULTIPLIERS;
    mealRatios?: { breakfast: number; lunch: number; dinner: number };
  }
): BMRCalculationResult {
  const bmr = calculateBMR(params);
  const tdee = calculateTDEE(bmr, params.activityLevel);
  const weightLoss = params.weight - params.targetWeight;
  const dailyCalorieLimit = calculateDailyCalorieLimit(tdee, weightLoss, params.targetDays);
  
  // 默认三餐比例
  const defaultRatios = { breakfast: 0.3, lunch: 0.4, dinner: 0.3 };
  const ratios = params.mealRatios || defaultRatios;
  
  const mealCalories = calculateMealCalories(dailyCalorieLimit, ratios);
  
  // 计算预期减重速度（kg/week）
  const weeklyDeficit = (tdee - dailyCalorieLimit) * 7;
  const weightLossRate = weeklyDeficit / 7700;
  
  return {
    bmr,
    tdee,
    dailyCalorieLimit,
    weightLossRate,
    mealCalories
  };
}

/**
 * 验证减重目标是否安全（与推荐算法保持一致）
 */
export function validateWeightLossGoal(
  currentWeight: number,
  targetWeight: number,
  targetDays: number
): { isValid: boolean; message?: string; isWarning?: boolean } {
  const weightLoss = currentWeight - targetWeight;
  const weeklyLoss = (weightLoss / targetDays) * 7;

  if (weightLoss <= 0) {
    return { isValid: false, message: '目标体重应低于当前体重' };
  }

  // 更宽松的验证标准，与推荐算法一致
  if (weeklyLoss > 1.2) {
    return { isValid: false, message: '减重速度过快，建议每周减重不超过1.2kg' };
  }

  if (weeklyLoss < 0.1) {
    return { isValid: false, message: '减重速度过慢，建议每周减重至少0.1kg' };
  }

  // 添加警告级别的提示（不阻止用户继续）
  if (weeklyLoss > 1.0) {
    return {
      isValid: true,
      isWarning: true,
      message: '减重速度较快，请注意营养均衡和身体状况'
    };
  }

  if (weeklyLoss < 0.3) {
    return {
      isValid: true,
      isWarning: true,
      message: '减重速度较慢，可以适当增加运动量'
    };
  }

  return { isValid: true };
}