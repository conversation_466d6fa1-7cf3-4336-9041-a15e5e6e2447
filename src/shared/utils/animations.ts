import anime from 'animejs';

// 基础动画类型定义
export interface AnimationOptions {
  duration?: number;
  delay?: number;
  easing?: string;
  direction?: 'normal' | 'reverse' | 'alternate';
  loop?: boolean | number;
  complete?: () => void;
}

// 基础淡入动画
export const fadeIn = (
  target: string | HTMLElement | NodeList,
  options: AnimationOptions = {}
) => {
  return anime({
    targets: target,
    opacity: [0, 1],
    duration: options.duration || 500,
    delay: options.delay || 0,
    easing: options.easing || 'easeOutQuart',
    complete: options.complete,
  });
};

// 向上滑入动画
export const slideUp = (
  target: string | HTMLElement | NodeList,
  options: AnimationOptions = {}
) => {
  return anime({
    targets: target,
    translateY: [20, 0],
    opacity: [0, 1],
    duration: options.duration || 600,
    delay: options.delay || 0,
    easing: options.easing || 'easeOutExpo',
    complete: options.complete,
  });
};

// 缩放入场动画
export const scaleIn = (
  target: string | HTMLElement | NodeList,
  options: AnimationOptions = {}
) => {
  return anime({
    targets: target,
    scale: [0.8, 1],
    opacity: [0, 1],
    duration: options.duration || 400,
    delay: options.delay || 0,
    easing: options.easing || 'easeOutBack',
    complete: options.complete,
  });
};

// 交错动画
export const staggerIn = (
  target: string | HTMLElement | NodeList,
  options: AnimationOptions & { stagger?: number } = {}
) => {
  return anime({
    targets: target,
    translateY: [30, 0],
    opacity: [0, 1],
    duration: options.duration || 800,
    delay: anime.stagger(options.stagger || 100),
    easing: options.easing || 'easeOutExpo',
    complete: options.complete,
  });
};

// 弹跳动画
export const bounce = (
  target: string | HTMLElement | NodeList,
  options: AnimationOptions = {}
) => {
  return anime({
    targets: target,
    translateY: [0, -10, 0],
    duration: options.duration || 600,
    delay: options.delay || 0,
    easing: 'easeInOutQuad',
    loop: options.loop || false,
    complete: options.complete,
  });
};

// 脉冲动画
export const pulse = (
  target: string | HTMLElement | NodeList,
  options: AnimationOptions = {}
) => {
  return anime({
    targets: target,
    scale: [1, 1.05, 1],
    duration: options.duration || 1000,
    delay: options.delay || 0,
    easing: 'easeInOutSine',
    loop: options.loop || true,
    complete: options.complete,
  });
};

// 进度条动画
export const progressBar = (
  target: string | HTMLElement,
  width: number,
  options: AnimationOptions = {}
) => {
  return anime({
    targets: target,
    width: `${width}%`,
    duration: options.duration || 1000,
    delay: options.delay || 0,
    easing: options.easing || 'easeOutExpo',
    complete: options.complete,
  });
};

// 数字计数动画
export const countUp = (
  target: HTMLElement,
  endValue: number,
  options: AnimationOptions & { startValue?: number } = {}
) => {
  const startValue = options.startValue || 0;
  const obj = { value: startValue };
  
  return anime({
    targets: obj,
    value: endValue,
    duration: options.duration || 1500,
    delay: options.delay || 0,
    easing: options.easing || 'easeOutExpo',
    round: 1,
    update: () => {
      target.textContent = Math.round(obj.value).toString();
    },
    complete: options.complete,
  });
};

// 摇摆动画（用于错误提示）
export const shake = (
  target: string | HTMLElement | NodeList,
  options: AnimationOptions = {}
) => {
  return anime({
    targets: target,
    translateX: [0, -10, 10, -10, 10, 0],
    duration: options.duration || 500,
    delay: options.delay || 0,
    easing: 'easeInOutSine',
    complete: options.complete,
  });
};

// 滚动触发动画
export const createScrollTrigger = (
  target: string | HTMLElement,
  animation: () => anime.AnimeInstance,
  options: { offset?: number; once?: boolean } = {}
) => {
  const element = typeof target === 'string' ? document.querySelector(target) : target;
  if (!element) return;

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          animation();
          if (options.once !== false) {
            observer.unobserve(entry.target);
          }
        }
      });
    },
    {
      threshold: 0.1,
      rootMargin: `${options.offset || 0}px`,
    }
  );

  observer.observe(element);
  return observer;
};

// 时间轴动画
export const createTimeline = (options: {
  targets?: string | HTMLElement | NodeList;
  autoplay?: boolean;
} = {}) => {
  return anime.timeline({
    autoplay: options.autoplay !== false,
  });
};

// 动画工具集合
export const animations = {
  fadeIn,
  slideUp,
  scaleIn,
  staggerIn,
  bounce,
  pulse,
  progressBar,
  countUp,
  shake,
  createScrollTrigger,
  createTimeline,
};

export default animations;