import { useEffect, useRef, useCallback, useState } from 'react';
import { animate, stagger } from 'animejs';
import { AnimationOptions, animations } from '../utils/animations';

// 基础动画Hook
export const useAnimation = () => {
  const animationRef = useRef<any>(null);

  const animateElement = useCallback((
    targets: string | HTMLElement | NodeList,
    config: any
  ) => {
    if (animationRef.current) {
      animationRef.current.pause();
    }
    animationRef.current = animate(targets, {
      ...config,
    });
    return animationRef.current;
  }, []);

  const stop = useCallback(() => {
    if (animationRef.current) {
      animationRef.current.pause();
      animationRef.current = null;
    }
  }, []);

  useEffect(() => {
    return () => {
      stop();
    };
  }, [stop]);

  return { animate: animateElement, stop };
};

// 淡入动画Hook
export const useFadeIn = (
  duration: number = 500,
  delay: number = 0
) => {
  const elementRef = useRef<HTMLElement>(null);
  const { animate } = useAnimation();

  useEffect(() => {
    if (elementRef.current) {
      animate(elementRef.current, {
        opacity: [0, 1],
        duration,
        delay,
        easing: 'easeOutQuart',
      });
    }
  }, [animate, duration, delay]);

  return elementRef;
};

// 滑入动画Hook
export const useSlideIn = (
  direction: 'up' | 'down' | 'left' | 'right' = 'up',
  duration: number = 600,
  delay: number = 0
) => {
  const elementRef = useRef<HTMLElement>(null);
  const { animate } = useAnimation();

  useEffect(() => {
    if (elementRef.current) {
      const translateProps: any = {};
      const distance = 20;

      switch (direction) {
        case 'up':
          translateProps.translateY = [distance, 0];
          break;
        case 'down':
          translateProps.translateY = [-distance, 0];
          break;
        case 'left':
          translateProps.translateX = [distance, 0];
          break;
        case 'right':
          translateProps.translateX = [-distance, 0];
          break;
      }

      animate(elementRef.current, {
        ...translateProps,
        opacity: [0, 1],
        duration,
        delay,
        easing: 'easeOutExpo',
      });
    }
  }, [animate, direction, duration, delay]);

  return elementRef;
};

// 交错动画Hook
export const useStagger = (
  itemSelector: string,
  staggerDelay: number = 100
) => {
  const containerRef = useRef<HTMLElement>(null);
  const { animate } = useAnimation();

  const triggerStagger = useCallback(() => {
    if (containerRef.current) {
      const items = containerRef.current.querySelectorAll(itemSelector);
      animate(items, {
        translateY: [30, 0],
        opacity: [0, 1],
        duration: 800,
        delay: stagger(staggerDelay),
        easing: 'easeOutExpo',
      });
    }
  }, [animate, itemSelector, staggerDelay]);

  return { containerRef, triggerStagger };
};

// 滚动触发动画Hook
export const useScrollAnimation = (
  animationType: 'fadeIn' | 'slideUp' | 'scaleIn' = 'fadeIn',
  options: { 
    threshold?: number; 
    rootMargin?: string; 
    once?: boolean;
  } = {}
) => {
  const elementRef = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!elementRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isVisible) {
            setIsVisible(true);
            
            // 执行动画
            switch (animationType) {
              case 'fadeIn':
                animations.fadeIn(entry.target as HTMLElement);
                break;
              case 'slideUp':
                animations.slideUp(entry.target as HTMLElement);
                break;
              case 'scaleIn':
                animations.scaleIn(entry.target as HTMLElement);
                break;
            }

            if (options.once !== false) {
              observer.unobserve(entry.target);
            }
          }
        });
      },
      {
        threshold: options.threshold || 0.1,
        rootMargin: options.rootMargin || '0px',
      }
    );

    observer.observe(elementRef.current);

    return () => {
      observer.disconnect();
    };
  }, [animationType, isVisible, options]);

  return elementRef;
};

// 悬停动画Hook
export const useHoverAnimation = (
  hoverConfig: any,
  leaveConfig: any
) => {
  const elementRef = useRef<HTMLElement>(null);
  const animationRef = useRef<any>(null);

  const handleMouseEnter = useCallback(() => {
    if (elementRef.current) {
      if (animationRef.current) {
        animationRef.current.pause();
      }
      animationRef.current = animate(elementRef.current, {
        ...hoverConfig,
      });
    }
  }, [hoverConfig]);

  const handleMouseLeave = useCallback(() => {
    if (elementRef.current) {
      if (animationRef.current) {
        animationRef.current.pause();
      }
      animationRef.current = animate(elementRef.current, {
        ...leaveConfig,
      });
    }
  }, [leaveConfig]);

  useEffect(() => {
    const element = elementRef.current;
    if (element) {
      element.addEventListener('mouseenter', handleMouseEnter);
      element.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        element.removeEventListener('mouseenter', handleMouseEnter);
        element.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
  }, [handleMouseEnter, handleMouseLeave]);

  return elementRef;
};

// 数字计数Hook
export const useCountUp = (
  endValue: number,
  options: {
    duration?: number;
    startValue?: number;
    trigger?: boolean;
  } = {}
) => {
  const [currentValue, setCurrentValue] = useState(options.startValue || 0);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (options.trigger !== false && elementRef.current) {
      const obj = { value: options.startValue || 0 };
      
      animate(obj, {
        value: endValue,
        duration: options.duration || 1500,
        easing: 'easeOutExpo',
        round: 1,
        update: () => {
          setCurrentValue(Math.round(obj.value));
        },
      });
    }
  }, [endValue, options.duration, options.startValue, options.trigger]);

  return { currentValue, elementRef };
};

// 进度条动画Hook
export const useProgressBar = (
  progress: number,
  options: {
    duration?: number;
    easing?: string;
  } = {}
) => {
  const [currentProgress, setCurrentProgress] = useState(0);
  const progressRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (progressRef.current) {
      const obj = { progress: currentProgress };
      
      animate(obj, {
        progress: progress,
        duration: options.duration || 1000,
        easing: options.easing || 'easeOutExpo',
        update: () => {
          setCurrentProgress(obj.progress);
          if (progressRef.current) {
            progressRef.current.style.width = `${obj.progress}%`;
          }
        },
      });
    }
  }, [progress, currentProgress, options.duration, options.easing]);

  return { currentProgress, progressRef };
};

// 支持prefers-reduced-motion的动画Hook
export const useAccessibleAnimation = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const createAccessibleAnimation = useCallback((
    targets: string | HTMLElement | NodeList,
    config: any
  ) => {
    if (prefersReducedMotion) {
      // 简化动画或直接设置最终状态
      const element = typeof targets === 'string' 
        ? document.querySelector(targets) 
        : targets instanceof NodeList 
        ? targets[0] 
        : targets;
      
      if (element && element instanceof HTMLElement) {
        if (config.opacity !== undefined) {
          const finalOpacity = Array.isArray(config.opacity) 
            ? config.opacity[config.opacity.length - 1] 
            : config.opacity;
          element.style.opacity = String(finalOpacity);
        }
      }
      return null;
    }

    return animate(targets, {
      ...config,
    });
  }, [prefersReducedMotion]);

  return { prefersReducedMotion, createAccessibleAnimation };
};