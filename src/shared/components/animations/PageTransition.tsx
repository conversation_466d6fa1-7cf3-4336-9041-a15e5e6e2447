import React, { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useAccessibleAnimation } from '@/shared/hooks/useAnimation';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
}

export const PageTransition: React.FC<PageTransitionProps> = ({ 
  children, 
  className = '' 
}) => {
  const location = useLocation();
  const containerRef = useRef<HTMLDivElement>(null);
  const { prefersReducedMotion, createAccessibleAnimation } = useAccessibleAnimation();
  const previousLocationRef = useRef(location.pathname);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Check if this is a route change
    const isRouteChange = previousLocationRef.current !== location.pathname;
    
    if (isRouteChange) {
      // Page transition animation
      createAccessibleAnimation(container, {
        opacity: [0, 1],
        translateY: [20, 0],
        duration: 600,
        easing: 'easeOutQuart',
        begin: () => {
          container.style.opacity = '0';
          container.style.transform = 'translateY(20px)';
        }
      });

      // Update previous location
      previousLocationRef.current = location.pathname;
    } else {
      // Initial load - just ensure visibility
      container.style.opacity = '1';
      container.style.transform = 'translateY(0)';
    }
  }, [location.pathname, createAccessibleAnimation]);

  return (
    <div
      ref={containerRef}
      className={`transform-gpu ${className}`}
      style={{ 
        opacity: prefersReducedMotion ? 1 : 0,
        transform: prefersReducedMotion ? 'none' : 'translateY(20px)'
      }}
    >
      {children}
    </div>
  );
};

export default PageTransition;