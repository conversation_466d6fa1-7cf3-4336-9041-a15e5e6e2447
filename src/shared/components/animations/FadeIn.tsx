import React, { useEffect, useRef } from 'react';
import anime from 'animejs';
import { useAccessibleAnimation } from '@/shared/hooks/useAnimation';

interface FadeInProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  distance?: number;
}

export const FadeIn: React.FC<FadeInProps> = ({
  children,
  className = '',
  delay = 0,
  duration = 600,
  direction = 'up',
  distance = 20
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const { prefersReducedMotion, createAccessibleAnimation } = useAccessibleAnimation();

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    if (prefersReducedMotion) {
      element.style.opacity = '1';
      element.style.transform = 'none';
      return;
    }

    // Set initial transform based on direction
    let initialTransform: any = {};
    switch (direction) {
      case 'up':
        initialTransform = { translateY: distance };
        break;
      case 'down':
        initialTransform = { translateY: -distance };
        break;
      case 'left':
        initialTransform = { translateX: distance };
        break;
      case 'right':
        initialTransform = { translateX: -distance };
        break;
      default:
        initialTransform = {};
    }

    // Set initial state
    anime.set(element, {
      opacity: 0,
      ...initialTransform
    });

    // Create fade in animation
    createAccessibleAnimation(element, {
      opacity: [0, 1],
      translateY: direction === 'up' || direction === 'down' ? [initialTransform.translateY || 0, 0] : 0,
      translateX: direction === 'left' || direction === 'right' ? [initialTransform.translateX || 0, 0] : 0,
      duration,
      delay,
      easing: 'easeOutQuart'
    });
  }, [delay, duration, direction, distance, prefersReducedMotion, createAccessibleAnimation]);

  return (
    <div
      ref={elementRef}
      className={`transform-gpu ${className}`}
      style={{
        opacity: prefersReducedMotion ? 1 : 0
      }}
    >
      {children}
    </div>
  );
};

export default FadeIn;