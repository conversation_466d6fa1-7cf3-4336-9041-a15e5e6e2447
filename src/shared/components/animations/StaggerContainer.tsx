import React, { useEffect, useRef } from 'react';
import anime from 'animejs';
import { useAccessibleAnimation } from '@/shared/hooks/useAnimation';

interface StaggerContainerProps {
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
  animationDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

export const StaggerContainer: React.FC<StaggerContainerProps> = ({
  children,
  className = '',
  staggerDelay = 100,
  animationDelay = 200,
  direction = 'up'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { prefersReducedMotion, createAccessibleAnimation } = useAccessibleAnimation();

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Find all direct children with data-stagger attribute
    const staggerElements = container.querySelectorAll('[data-stagger]');
    
    if (staggerElements.length === 0) return;

    // Set initial state for all elements
    if (!prefersReducedMotion) {
      anime.set(staggerElements, {
        opacity: 0,
        translateY: direction === 'up' ? 30 : direction === 'down' ? -30 : 0,
        translateX: direction === 'left' ? 30 : direction === 'right' ? -30 : 0,
      });
    }

    // Create stagger animation
    setTimeout(() => {
      createAccessibleAnimation(staggerElements, {
        opacity: [0, 1],
        translateY: [
          direction === 'up' ? 30 : direction === 'down' ? -30 : 0,
          0
        ],
        translateX: [
          direction === 'left' ? 30 : direction === 'right' ? -30 : 0,
          0
        ],
        duration: 800,
        delay: anime.stagger(staggerDelay),
        easing: 'easeOutQuart'
      });
    }, animationDelay);
  }, [staggerDelay, animationDelay, direction, prefersReducedMotion, createAccessibleAnimation]);

  return (
    <div ref={containerRef} className={`transform-gpu ${className}`}>
      {children}
    </div>
  );
};

export default StaggerContainer;