import React, { useEffect, useRef } from 'react';
import { animate, stagger } from 'animejs';
import { useAccessibleAnimation } from '@/shared/hooks/useAnimation';

interface StaggerContainerProps {
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
  animationDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

export const StaggerContainer: React.FC<StaggerContainerProps> = ({
  children,
  className = '',
  staggerDelay = 100,
  animationDelay = 200,
  direction = 'up'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { prefersReducedMotion, createAccessibleAnimation } = useAccessibleAnimation();

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Find all direct children with data-stagger attribute
    const staggerElements = container.querySelectorAll('[data-stagger]');
    
    if (staggerElements.length === 0) return;

    // Set initial state for all elements
    if (!prefersReducedMotion) {
      staggerElements.forEach((element: Element) => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.opacity = '0';
        if (direction === 'up') htmlElement.style.transform = 'translateY(30px)';
        else if (direction === 'down') htmlElement.style.transform = 'translateY(-30px)';
        else if (direction === 'left') htmlElement.style.transform = 'translateX(30px)';
        else if (direction === 'right') htmlElement.style.transform = 'translateX(-30px)';
      });
    }

    // Create stagger animation
    setTimeout(() => {
      createAccessibleAnimation(staggerElements, {
        opacity: [0, 1],
        translateY: [
          direction === 'up' ? 30 : direction === 'down' ? -30 : 0,
          0
        ],
        translateX: [
          direction === 'left' ? 30 : direction === 'right' ? -30 : 0,
          0
        ],
        duration: 800,
        delay: stagger(staggerDelay),
        easing: 'easeOutQuart'
      });
    }, animationDelay);
  }, [staggerDelay, animationDelay, direction, prefersReducedMotion, createAccessibleAnimation]);

  return (
    <div ref={containerRef} className={`transform-gpu ${className}`}>
      {children}
    </div>
  );
};

export default StaggerContainer;