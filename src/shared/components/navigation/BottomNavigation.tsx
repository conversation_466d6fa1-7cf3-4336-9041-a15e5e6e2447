import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

interface BottomNavigationProps {
  onRecordFood: () => void;
  onViewProfile: () => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  onRecordFood,
  onViewProfile
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
      {/* 临时简化版本，准备重构为DaisyUI 5.0 Dock组件 */}
      <div className="flex items-center justify-around p-4">
        <button
          className={`flex flex-col items-center gap-1 p-2 ${
            isActive('/record') ? 'text-blue-600' : 'text-gray-600'
          }`}
          onClick={() => onRecordFood()}
        >
          <span className="text-xl">🍽️</span>
          <span className="text-xs">开始记录</span>
        </button>

        <button
          className={`flex flex-col items-center gap-1 p-2 ${
            isActive('/calendar') ? 'text-blue-600' : 'text-gray-600'
          }`}
          onClick={() => navigate('/calendar')}
        >
          <span className="text-xl">📅</span>
          <span className="text-xs">查看日历</span>
        </button>

        <button
          className={`flex flex-col items-center gap-1 p-2 ${
            isActive('/profile') ? 'text-blue-600' : 'text-gray-600'
          }`}
          onClick={() => onViewProfile()}
        >
          <span className="text-xl">👤</span>
          <span className="text-xs">我的</span>
        </button>
      </div>
    </nav>
  );
};

export default BottomNavigation;