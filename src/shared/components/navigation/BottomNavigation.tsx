import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

interface BottomNavigationProps {
  onRecordFood: () => void;
  onViewProfile: () => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  onRecordFood,
  onViewProfile
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    const button = e.currentTarget;
    // 简单的点击动画
    button.style.transform = 'scale(0.95)';
    setTimeout(() => {
      button.style.transform = 'scale(1)';
    }, 150);
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // 基于Context7最佳实践的简洁实现 - 增强z-index确保固定
  return (
    <div className="fixed bottom-0 inset-x-0 z-[9999] bg-white/95 backdrop-blur-sm border-t border-gray-200/50 shadow-lg pb-safe">
      <div className="flex items-center justify-around px-4 py-2 min-h-16">
        {/* 开始记录按钮 */}
        <button
          className={`flex flex-col items-center justify-center gap-1 px-3 py-2 rounded-xl border-none transition-all duration-200 min-w-14 min-h-12 text-sm font-medium ${
            isActive('/record')
              ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white'
              : 'bg-transparent text-gray-700 hover:bg-gray-100'
          }`}
          onClick={(e) => {
            handleButtonClick(e);
            onRecordFood();
          }}
        >
          <span className="text-xl">🍽️</span>
          <span className="text-xs leading-none">开始记录</span>
        </button>

        {/* 查看日历按钮 */}
        <button
          className={`flex flex-col items-center justify-center gap-1 px-3 py-2 rounded-xl border-none transition-all duration-200 min-w-14 min-h-12 text-sm font-medium ${
            isActive('/calendar')
              ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white'
              : 'bg-transparent text-gray-700 hover:bg-gray-100'
          }`}
          onClick={(e) => {
            handleButtonClick(e);
            navigate('/calendar');
          }}
        >
          <span className="text-xl">📅</span>
          <span className="text-xs leading-none">查看日历</span>
        </button>

        {/* 我的按钮 */}
        <button
          className={`flex flex-col items-center justify-center gap-1 px-3 py-2 rounded-xl border-none transition-all duration-200 min-w-14 min-h-12 text-sm font-medium ${
            isActive('/profile')
              ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white'
              : 'bg-transparent text-gray-700 hover:bg-gray-100'
          }`}
          onClick={(e) => {
            handleButtonClick(e);
            onViewProfile();
          }}
        >
          <span className="text-xl">👤</span>
          <span className="text-xs leading-none">我的</span>
        </button>
      </div>
    </div>
  );
};

export default BottomNavigation;