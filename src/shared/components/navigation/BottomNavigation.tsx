// 移动端固定底部导航栏 - 层叠上下文冲突完整解决方案
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

interface BottomNavigationProps {
  onRecordFood: () => void;
  onViewProfile: () => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  onRecordFood,
  onViewProfile
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  // 原生App级别的触控反馈
  const handleTouchFeedback = (e: React.TouchEvent<HTMLButtonElement>) => {
    const button = e.currentTarget;
    button.style.transform = 'scale(0.95)';
    button.style.opacity = '0.8';

    setTimeout(() => {
      button.style.transform = 'scale(1)';
      button.style.opacity = '1';
    }, 100);
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <>
      {/*
        底部导航栏 - 层叠上下文冲突完整解决方案

        问题：Anime.js的transform属性创建新的层叠上下文，导致z-index失效
        解决：将BottomNavigation移到根级别，脱离transform影响范围
        技术：使用32位整数最大值z-index确保绝对最高层级
      */}
      <nav
        className="bottom-navigation fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-gray-200/50 shadow-2xl"
        style={{
          // 安全区域适配 - 支持全面屏设备
          paddingBottom: 'max(env(safe-area-inset-bottom), 8px)',
          // 绝对固定定位 - 相对于视口而非父元素
          position: 'fixed !important',
          bottom: '0 !important',
          left: '0 !important',
          right: '0 !important',
          // 绝对最高层级 - 32位整数最大值
          zIndex: '2147483647 !important',
          // 层叠上下文隔离
          isolation: 'isolate',
          // 最小高度保障
          minHeight: '72px',
          // 强制可视性保障
          display: 'block !important',
          visibility: 'visible !important',
          opacity: '1 !important'
        }}
      >
        {/* 原生App级别的按钮容器 - 拇指友好设计 */}
        <div className="flex items-center justify-around px-6 py-3 min-h-[72px]">
          
          {/* 开始记录按钮 - 拇指友好设计 */}
          <button
            className={`flex flex-col items-center justify-center gap-1 px-5 py-4 rounded-2xl transition-all duration-300 min-w-[64px] min-h-[52px] text-sm font-medium touch-manipulation ${
              isActive('/record')
                ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg scale-105'
                : 'bg-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200 hover:scale-105'
            }`}
            onTouchStart={handleTouchFeedback}
            onClick={() => onRecordFood()}
            aria-label="开始记录食物"
          >
            <span className="text-xl">🍽️</span>
            <span className="text-xs leading-none font-medium">开始记录</span>
          </button>

          {/* 查看日历按钮 */}
          <button
            className={`flex flex-col items-center justify-center gap-1 px-5 py-4 rounded-2xl transition-all duration-300 min-w-[64px] min-h-[52px] text-sm font-medium touch-manipulation ${
              isActive('/calendar')
                ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg scale-105'
                : 'bg-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200 hover:scale-105'
            }`}
            onTouchStart={handleTouchFeedback}
            onClick={() => navigate('/calendar')}
            aria-label="查看日历"
          >
            <span className="text-xl">📅</span>
            <span className="text-xs leading-none font-medium">查看日历</span>
          </button>

          {/* 我的按钮 */}
          <button
            className={`flex flex-col items-center justify-center gap-1 px-5 py-4 rounded-2xl transition-all duration-300 min-w-[64px] min-h-[52px] text-sm font-medium touch-manipulation ${
              isActive('/profile')
                ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg scale-105'
                : 'bg-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200 hover:scale-105'
            }`}
            onTouchStart={handleTouchFeedback}
            onClick={() => onViewProfile()}
            aria-label="我的"
          >
            <span className="text-xl">👤</span>
            <span className="text-xs leading-none font-medium">我的</span>
          </button>
        </div>
      </nav>
    </>
  );
};

export default BottomNavigation;