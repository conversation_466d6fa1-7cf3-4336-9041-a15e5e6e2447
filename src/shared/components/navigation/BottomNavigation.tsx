import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

interface BottomNavigationProps {
  onRecordFood: () => void;
  onViewProfile: () => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({ 
  onRecordFood, 
  onViewProfile 
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    const button = e.currentTarget;
    // 点击动画效果
    button.style.transform = 'scale(0.95)';
    setTimeout(() => {
      button.style.transform = 'scale(1)';
    }, 150);
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-t border-gray-200/50 shadow-lg pb-safe">
      <div className="dock">
        {/* 开始记录食物按钮 */}
        <button
          className={`${isActive('/record') ? 'dock-active' : ''}`}
          onClick={(e) => {
            handleButtonClick(e);
            onRecordFood();
          }}
        >
          <span className="text-lg">🍽️</span>
          <span className="dock-label">开始记录</span>
        </button>

        {/* 查看日历按钮 */}
        <button
          className={`${isActive('/calendar') ? 'dock-active' : ''}`}
          onClick={(e) => {
            handleButtonClick(e);
            navigate('/calendar');
          }}
        >
          <span className="text-lg">📅</span>
          <span className="dock-label">查看日历</span>
        </button>

        {/* 我的按钮 */}
        <button
          className={`${isActive('/profile') ? 'dock-active' : ''}`}
          onClick={(e) => {
            handleButtonClick(e);
            onViewProfile();
          }}
        >
          <span className="text-lg">👤</span>
          <span className="dock-label">我的</span>
        </button>
      </div>
    </div>
  );
};

export default BottomNavigation;