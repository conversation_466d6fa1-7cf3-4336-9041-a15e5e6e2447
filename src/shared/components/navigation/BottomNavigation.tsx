import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { useNavigate, useLocation } from 'react-router-dom';

interface BottomNavigationProps {
  onRecordFood: () => void;
  onViewProfile: () => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({ 
  onRecordFood, 
  onViewProfile 
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 创建底部导航容器并直接挂载到body
    let navContainer = document.getElementById('bottom-navigation-portal');
    
    if (!navContainer) {
      navContainer = document.createElement('div');
      navContainer.id = 'bottom-navigation-portal';
      
      // 直接设置关键样式到DOM元素
      navContainer.style.position = 'fixed';
      navContainer.style.bottom = '0';
      navContainer.style.left = '0';
      navContainer.style.right = '0';
      navContainer.style.zIndex = '99999';
      navContainer.style.pointerEvents = 'auto';
      navContainer.style.isolation = 'isolate';
      
      document.body.appendChild(navContainer);
    }

    return () => {
      // 组件卸载时清理
      const existingContainer = document.getElementById('bottom-navigation-portal');
      if (existingContainer) {
        document.body.removeChild(existingContainer);
      }
    };
  }, []);

  const handleButtonClick = (callback: () => void) => {
    callback();
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const navigationContent = (
    <div
      ref={containerRef}
      style={{
        background: 'linear-gradient(180deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.98) 100%)',
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
        borderTop: '1px solid rgba(0,0,0,0.1)',
        boxShadow: '0 -8px 32px rgba(0,0,0,0.12)',
        padding: '8px 16px calc(8px + env(safe-area-inset-bottom))',
        minHeight: '64px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-around',
        gap: '8px'
      }}
    >
      {/* 开始记录按钮 */}
      <button
        onClick={() => handleButtonClick(onRecordFood)}
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '4px',
          padding: '8px 12px',
          borderRadius: '12px',
          border: 'none',
          background: isActive('/record') 
            ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
            : 'transparent',
          color: isActive('/record') ? 'white' : '#374151',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          minWidth: '56px',
          minHeight: '48px',
          fontSize: '14px',
          fontWeight: '500'
        }}
        onMouseEnter={(e) => {
          if (!isActive('/record')) {
            e.currentTarget.style.background = 'rgba(0,0,0,0.05)';
          }
        }}
        onMouseLeave={(e) => {
          if (!isActive('/record')) {
            e.currentTarget.style.background = 'transparent';
          }
        }}
      >
        <span style={{ fontSize: '20px' }}>🍽️</span>
        <span style={{ fontSize: '12px', lineHeight: '1' }}>开始记录</span>
      </button>

      {/* 查看日历按钮 */}
      <button
        onClick={() => handleButtonClick(() => navigate('/calendar'))}
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '4px',
          padding: '8px 12px',
          borderRadius: '12px',
          border: 'none',
          background: isActive('/calendar') 
            ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
            : 'transparent',
          color: isActive('/calendar') ? 'white' : '#374151',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          minWidth: '56px',
          minHeight: '48px',
          fontSize: '14px',
          fontWeight: '500'
        }}
        onMouseEnter={(e) => {
          if (!isActive('/calendar')) {
            e.currentTarget.style.background = 'rgba(0,0,0,0.05)';
          }
        }}
        onMouseLeave={(e) => {
          if (!isActive('/calendar')) {
            e.currentTarget.style.background = 'transparent';
          }
        }}
      >
        <span style={{ fontSize: '20px' }}>📅</span>
        <span style={{ fontSize: '12px', lineHeight: '1' }}>查看日历</span>
      </button>

      {/* 我的按钮 */}
      <button
        onClick={() => handleButtonClick(onViewProfile)}
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '4px',
          padding: '8px 12px',
          borderRadius: '12px',
          border: 'none',
          background: isActive('/profile') 
            ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
            : 'transparent',
          color: isActive('/profile') ? 'white' : '#374151',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          minWidth: '56px',
          minHeight: '48px',
          fontSize: '14px',
          fontWeight: '500'
        }}
        onMouseEnter={(e) => {
          if (!isActive('/profile')) {
            e.currentTarget.style.background = 'rgba(0,0,0,0.05)';
          }
        }}
        onMouseLeave={(e) => {
          if (!isActive('/profile')) {
            e.currentTarget.style.background = 'transparent';
          }
        }}
      >
        <span style={{ fontSize: '20px' }}>👤</span>
        <span style={{ fontSize: '12px', lineHeight: '1' }}>我的</span>
      </button>
    </div>
  );

  // 使用Portal将导航栏渲染到body下的专用容器中
  const portalContainer = document.getElementById('bottom-navigation-portal');
  
  return portalContainer ? createPortal(navigationContent, portalContainer) : null;
};

export default BottomNavigation;