// 移动端固定底部导航栏 - 始终显示，拇指友好设计
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

interface BottomNavigationProps {
  onRecordFood: () => void;
  onViewProfile: () => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  onRecordFood,
  onViewProfile
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  // 原生App级别的触控反馈
  const handleTouchFeedback = (e: React.TouchEvent<HTMLButtonElement>) => {
    const button = e.currentTarget;
    button.style.transform = 'scale(0.95)';
    button.style.opacity = '0.8';

    setTimeout(() => {
      button.style.transform = 'scale(1)';
      button.style.opacity = '1';
    }, 100);
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <>
      {/* 真正的固定底部导航栏 - 最高层级显示 */}
      <nav
        className="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-gray-200/50 shadow-2xl"
        style={{
          // 使用Context7推荐的安全区域适配
          paddingBottom: 'max(env(safe-area-inset-bottom), 8px)',
          // 确保真正固定定位和绝对最高层级
          position: 'fixed !important',
          bottom: '0 !important',
          left: '0 !important',
          right: '0 !important',
          zIndex: '2147483647 !important', // 32位整数最大值，绝对最高层级
          // 强制显示在最顶层
          isolation: 'isolate',
          // 最小高度保障
          minHeight: '72px',
          // 强制可视性
          display: 'block !important',
          visibility: 'visible !important',
          opacity: '1 !important'
        }}
      >
        {/* 原生App级别的按钮容器 - 拇指友好设计 */}
        <div className="flex items-center justify-around px-6 py-3 min-h-[72px]">
          
          {/* 开始记录按钮 - 拇指友好设计 */}
          <button
            className={`flex flex-col items-center justify-center gap-1 px-5 py-4 rounded-2xl transition-all duration-300 min-w-[64px] min-h-[52px] text-sm font-medium touch-manipulation ${
              isActive('/record')
                ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg scale-105'
                : 'bg-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200 hover:scale-105'
            }`}
            onTouchStart={handleTouchFeedback}
            onClick={() => onRecordFood()}
            aria-label="开始记录食物"
          >
            <span className="text-xl">🍽️</span>
            <span className="text-xs leading-none font-medium">开始记录</span>
          </button>

          {/* 查看日历按钮 */}
          <button
            className={`flex flex-col items-center justify-center gap-1 px-5 py-4 rounded-2xl transition-all duration-300 min-w-[64px] min-h-[52px] text-sm font-medium touch-manipulation ${
              isActive('/calendar')
                ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg scale-105'
                : 'bg-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200 hover:scale-105'
            }`}
            onTouchStart={handleTouchFeedback}
            onClick={() => navigate('/calendar')}
            aria-label="查看日历"
          >
            <span className="text-xl">📅</span>
            <span className="text-xs leading-none font-medium">查看日历</span>
          </button>

          {/* 我的按钮 */}
          <button
            className={`flex flex-col items-center justify-center gap-1 px-5 py-4 rounded-2xl transition-all duration-300 min-w-[64px] min-h-[52px] text-sm font-medium touch-manipulation ${
              isActive('/profile')
                ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg scale-105'
                : 'bg-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200 hover:scale-105'
            }`}
            onTouchStart={handleTouchFeedback}
            onClick={() => onViewProfile()}
            aria-label="我的"
          >
            <span className="text-xl">👤</span>
            <span className="text-xs leading-none font-medium">我的</span>
          </button>
        </div>
      </nav>
    </>
  );
};

export default BottomNavigation;