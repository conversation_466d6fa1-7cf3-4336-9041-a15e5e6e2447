import React from 'react';
import { cn } from '@/shared/utils/format';
import { useHoverAnimation, useAccessibleAnimation } from '@/shared/hooks/useAnimation';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  fullWidth?: boolean;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant = 'primary', 
    size = 'md', 
    loading = false,
    fullWidth = false,
    disabled,
    children, 
    ...props 
  }, ref) => {
    const { createAccessibleAnimation } = useAccessibleAnimation();

    // 悬停动画配置
    const hoverRef = useHoverAnimation(
      {
        scale: [1, 1.02],
        duration: 200,
        easing: 'easeOutQuart',
      },
      {
        scale: [1.02, 1],
        duration: 200,
        easing: 'easeOutQuart',
      }
    );

    const baseClasses = [
      'inline-flex items-center justify-center rounded-lg font-medium',
      'transition-all duration-200 ease-out',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'touch-manipulation', // 移动端优化
      'transform-gpu', // 硬件加速
      'hover:scale-[1.02] active:scale-[0.98]', // CSS fallback for reduced motion
      'select-none', // 防止文本选择
    ];

    const variantClasses = {
      primary: [
        'text-white shadow-sm font-medium',
        'focus:ring-2 focus:ring-green-300 focus:ring-offset-2'
      ],
      secondary: [
        'bg-gray-100 text-gray-900 border border-gray-300',
        'hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500',
        'active:bg-gray-300'
      ],
      danger: [
        'text-white shadow-sm',
        'focus:ring-2 focus:ring-red-300 focus:ring-offset-2'
      ],
      ghost: [
        'text-gray-700 bg-transparent',
        'hover:bg-gray-100 hover:text-gray-900 focus:ring-gray-500',
        'active:bg-gray-200'
      ]
    };

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg'
    };

    const classes = cn(
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      fullWidth && 'w-full',
      loading && 'cursor-wait',
      className
    );

    // 获取内联样式
    const getInlineStyles = () => {
      const baseStyle: React.CSSProperties = {};

      if (variant === 'primary') {
        baseStyle.backgroundColor = '#16a34a'; // green-600
        baseStyle.color = '#ffffff';
      } else if (variant === 'danger') {
        baseStyle.backgroundColor = '#dc2626'; // red-600
        baseStyle.color = '#ffffff';
      }

      return baseStyle;
    };

    // 点击动画处理
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (!disabled && !loading) {
        // 创建点击涟漪效果
        createAccessibleAnimation(e.currentTarget, {
          scale: [1, 0.95, 1],
          duration: 150,
          easing: 'easeOutQuart',
        });
      }
      props.onClick?.(e);
    };

    return (
      <button
        className={classes}
        style={getInlineStyles()}
        ref={(node) => {
          // 合并refs
          if (typeof ref === 'function') {
            ref(node);
          } else if (ref) {
            ref.current = node;
          }
          hoverRef.current = node;
        }}
        disabled={disabled || loading}
        onClick={handleClick}
        onMouseEnter={(e) => {
          if (variant === 'primary') {
            e.currentTarget.style.backgroundColor = '#15803d'; // green-700
          } else if (variant === 'danger') {
            e.currentTarget.style.backgroundColor = '#b91c1c'; // red-700
          }
        }}
        onMouseLeave={(e) => {
          if (variant === 'primary') {
            e.currentTarget.style.backgroundColor = '#16a34a'; // green-600
          } else if (variant === 'danger') {
            e.currentTarget.style.backgroundColor = '#dc2626'; // red-600
          }
        }}
        {...props}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        <span className="relative">
          {children}
        </span>
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;