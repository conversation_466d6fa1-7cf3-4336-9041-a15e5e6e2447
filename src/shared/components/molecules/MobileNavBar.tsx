import React from 'react';
import { cn } from '@/shared/utils/format';

interface MobileNavBarProps {
  title: string;
  onBack?: () => void;
  showBack?: boolean;
  rightAction?: React.ReactNode;
  className?: string;
}

const MobileNavBar: React.FC<MobileNavBarProps> = ({
  title,
  onBack,
  showBack = false,
  rightAction,
  className
}) => {
  return (
    <div className={cn(
      'sticky top-0 z-50 bg-white border-b border-gray-200',
      'safe-area-inset px-4 py-3',
      'flex items-center justify-between',
      'min-h-[56px]', // 确保足够的触控区域
      className
    )}>
      {/* 左侧：返回按钮或占位 */}
      <div className="flex items-center w-12">
        {showBack && onBack && (
          <button
            onClick={onBack}
            className={cn(
              'flex items-center justify-center',
              'w-10 h-10 rounded-full', // 44px触控区域
              'text-gray-600 hover:text-gray-900',
              'hover:bg-gray-100 active:bg-gray-200',
              'transition-colors duration-200',
              'touch-manipulation'
            )}
            aria-label="返回"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
        )}
      </div>

      {/* 中间：标题 */}
      <div className="flex-1 text-center px-4">
        <h1 className="text-lg font-semibold text-gray-900 truncate">
          {title}
        </h1>
      </div>

      {/* 右侧：操作按钮或占位 */}
      <div className="flex items-center w-12 justify-end">
        {rightAction}
      </div>
    </div>
  );
};

export default MobileNavBar;

// 移动端步骤指示器组件
interface MobileStepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  className?: string;
}

export const MobileStepIndicator: React.FC<MobileStepIndicatorProps> = ({
  currentStep,
  totalSteps,
  className
}) => {
  return (
    <div className={cn('px-4 py-3', className)}>
      {/* 步骤圆点 */}
      <div className="flex items-center justify-center mb-3">
        {Array.from({ length: totalSteps }, (_, i) => (
          <React.Fragment key={i}>
            <div
              className={cn(
                'w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold',
                'transition-all duration-300',
                i + 1 <= currentStep
                  ? 'text-white shadow-sm'
                  : 'bg-gray-200 text-gray-500'
              )}
              style={i + 1 <= currentStep ? { backgroundColor: '#16a34a' } : {}}
            >
              {i + 1}
            </div>
            {i < totalSteps - 1 && (
              <div className="flex-1 h-0.5 mx-2 bg-gray-200">
                <div
                  className="h-full transition-all duration-300"
                  style={{
                    width: i + 1 < currentStep ? '100%' : '0%',
                    backgroundColor: '#16a34a'
                  }}
                />
              </div>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* 进度条 */}
      <div className="w-full bg-gray-200 rounded-full h-1">
        <div
          className="h-1 rounded-full transition-all duration-300"
          style={{
            width: `${(currentStep / totalSteps) * 100}%`,
            backgroundColor: '#16a34a'
          }}
        />
      </div>
    </div>
  );
};
