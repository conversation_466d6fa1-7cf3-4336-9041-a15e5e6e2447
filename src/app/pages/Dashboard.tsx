import React, { useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { animate, createScope, stagger, utils } from 'animejs';
import { useUserStore } from '@/domains/user/stores/userStore';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { NutritionCard, CalorieRing, NutritionAdvice } from '@/domains/nutrition';
import { formatCalories, formatWeight, formatDate } from '@/shared/utils';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile, clearProfile } = useUserStore();
  const { getDailySummary, updateDailySummary } = useNutritionStore();

  // Anime.js refs and scope
  const containerRef = useRef<HTMLDivElement>(null);
  const scope = useRef<any>(null);

  const today = new Date();
  const todaySummary = getDailySummary(today);

  // Anime.js动画系统初始化
  useEffect(() => {
    if (!containerRef.current) return;

    scope.current = createScope({
      root: containerRef.current,
      defaults: {
        duration: 600,
        ease: 'out(3)',
      },
      mediaQueries: {
        mobile: '(max-width: 640px)',
        reducedMotion: '(prefers-reduced-motion)',
      }
    }).add(ctx => {
      const { mobile, reducedMotion } = ctx.matches;

      // 页面加载动画
      if (!reducedMotion) {
        // 头部区域动画
        animate('.hero-section', {
          opacity: [0, 1],
          translateY: [30, 0],
          duration: 800,
          delay: 100,
        });

        // 卡片进入动画
        animate('.dashboard-card', {
          opacity: [0, 1],
          translateY: [40, 0],
          scale: [0.95, 1],
          duration: 700,
          delay: stagger(150, { start: 300 }),
        });

        // 按钮动画
        animate('.action-button', {
          opacity: [0, 1],
          translateY: [20, 0],
          duration: 500,
          delay: stagger(100, { start: 800 }),
        });
      }
    });

    return () => scope.current?.revert();
  }, []);

  // 初始化今日营养数据
  useEffect(() => {
    if (profile && !todaySummary) {
      // 创建默认的每日汇总
      updateDailySummary(today, {
        totalCalories: 0,
        calorieLimit: profile.dailyCalorieLimit,
        remainingCalories: profile.dailyCalorieLimit,
        mealBreakdown: {
          breakfast: {
            mealType: 'breakfast',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.breakfast),
            foodCount: 0,
            percentage: 0
          },
          lunch: {
            mealType: 'lunch',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.lunch),
            foodCount: 0,
            percentage: 0
          },
          dinner: {
            mealType: 'dinner',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.dinner),
            foodCount: 0,
            percentage: 0
          },
          snack: {
            mealType: 'snack',
            calories: 0,
            calorieLimit: 0,
            foodCount: 0,
            percentage: 0
          }
        },
        nutrition: {
          protein: 0,
          fat: 0,
          carbs: 0,
          fiber: 0,
          sugar: 0
        },
        status: 'under',
        percentage: 0
      });
    }
  }, [profile, todaySummary, updateDailySummary, today]);

  // 交互动画函数
  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    const button = e.currentTarget;
    animate(button, {
      scale: [1, 0.95, 1],
      duration: 200,
      ease: 'out(2)',
    });
  };

  const handleCardHover = (e: React.MouseEvent<HTMLDivElement>) => {
    const card = e.currentTarget;
    animate(card, {
      translateY: [0, -4],
      scale: [1, 1.02],
      duration: 300,
      ease: 'out(3)',
    });
  };

  const handleCardLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    const card = e.currentTarget;
    animate(card, {
      translateY: [card.style.transform.includes('translateY') ? -4 : 0, 0],
      scale: [1.02, 1],
      duration: 300,
      ease: 'out(3)',
    });
  };

  if (!profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
            <span className="text-2xl">👤</span>
          </div>
          <h2 className="text-xl font-bold text-slate-800 mb-2">
            未找到用户档案
          </h2>
          <p className="text-slate-600 mb-4">
            请先完成个人档案设置
          </p>
          <button
            className="btn btn-primary btn-lg"
            onClick={() => navigate('/setup')}
          >
            立即设置
          </button>
        </div>
      </div>
    );
  }

  const weightLoss = profile.weight - profile.targetWeight;
  const weeklyLoss = (weightLoss / profile.targetDays) * 7;

  // 模拟添加一些测试数据
  const addTestData = () => {
    if (todaySummary) {
      const testCalories = 650;
      updateDailySummary(today, {
        totalCalories: todaySummary.totalCalories + testCalories,
        mealBreakdown: {
          ...todaySummary.mealBreakdown,
          breakfast: {
            ...todaySummary.mealBreakdown.breakfast,
            calories: todaySummary.mealBreakdown.breakfast.calories + testCalories,
            foodCount: todaySummary.mealBreakdown.breakfast.foodCount + 1
          }
        },
        nutrition: {
          protein: todaySummary.nutrition.protein + 25,
          fat: todaySummary.nutrition.fat + 15,
          carbs: todaySummary.nutrition.carbs + 45,
          fiber: todaySummary.nutrition.fiber + 5,
          sugar: todaySummary.nutrition.sugar + 10
        }
      });
    }
  };

  return (
    <div
      ref={containerRef}
      className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50"
    >
      {/* 移动端优先的容器设计 */}
      <div className="w-full max-w-none sm:max-w-lg md:max-w-2xl lg:max-w-4xl xl:max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8">

        {/* 现代化欢迎信息 - 移动端优化 */}
        <div className="hero-section text-center mb-6 sm:mb-8 lg:mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl mb-4 sm:mb-6">
            <span className="text-2xl">🎯</span>
          </div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2 sm:mb-3">
            {profile.name ? `你好，${profile.name}` : '欢迎使用 KCal Tracker'}
          </h1>
          <p className="text-slate-600 text-base sm:text-lg lg:text-xl font-medium mb-3 sm:mb-4">
            {formatDate(today, 'yyyy年MM月dd日')}
          </p>
          <p className="text-slate-500 text-sm sm:text-base mb-4">
            今天是您健康之旅的新一天
          </p>
          <div className="inline-flex items-center gap-2 px-3 py-2 sm:px-4 bg-white/60 backdrop-blur-sm rounded-full border border-white/20">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs sm:text-sm font-medium text-slate-700">系统运行正常</span>
          </div>
        </div>

        {/* 今日营养概览 - 移动端优先设计 */}
        {todaySummary && (
          <div className="space-y-4 sm:space-y-6">
            {/* 卡路里环形图卡片 - 移动端优化 */}
            <div
              className="dashboard-card bg-white/90 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-white/20 shadow-lg"
              onMouseEnter={handleCardHover}
              onMouseLeave={handleCardLeave}
            >
              <div className="text-center mb-4">
                <h3 className="text-lg sm:text-xl font-bold text-slate-800 mb-1 flex items-center justify-center gap-2">
                  <span className="text-xl">🔥</span>
                  今日卡路里
                </h3>
                <p className="text-sm text-slate-600">
                  距离目标还有 <span className="font-semibold text-indigo-600">{todaySummary.calorieLimit - todaySummary.totalCalories}</span> 卡
                </p>
              </div>

              <div className="flex justify-center mb-4">
                <CalorieRing
                  current={todaySummary.totalCalories}
                  target={todaySummary.calorieLimit}
                  size="lg"
                />
              </div>

              <div className="grid grid-cols-2 gap-3 sm:gap-4">
                <div className="stats shadow-sm bg-gradient-to-r from-green-50 to-emerald-50">
                  <div className="stat p-3 sm:p-4 text-center">
                    <div className="stat-value text-lg sm:text-xl text-green-700">{todaySummary.totalCalories}</div>
                    <div className="stat-desc text-xs text-green-600">已摄入</div>
                  </div>
                </div>
                <div className="stats shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50">
                  <div className="stat p-3 sm:p-4 text-center">
                    <div className="stat-value text-lg sm:text-xl text-blue-700">{todaySummary.calorieLimit}</div>
                    <div className="stat-desc text-xs text-blue-600">目标</div>
                  </div>
                </div>
              </div>
            </div>

            {/* 营养详情卡片 - 移动端优化 */}
            <div
              className="dashboard-card bg-white/90 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-white/20 shadow-lg"
              onMouseEnter={handleCardHover}
              onMouseLeave={handleCardLeave}
            >
              <h3 className="text-lg sm:text-xl font-bold text-slate-800 mb-4 flex items-center gap-2">
                <span className="text-xl">📊</span>
                营养详情
              </h3>
              <NutritionCard summary={todaySummary} />
            </div>

            {/* 营养建议卡片 - 移动端优化 */}
            {todaySummary && (
              <div
                className="dashboard-card bg-white/90 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-white/20 shadow-lg"
                onMouseEnter={handleCardHover}
                onMouseLeave={handleCardLeave}
              >
                <h3 className="text-lg sm:text-xl font-bold text-slate-800 mb-4 flex items-center gap-2">
                  <span className="text-xl">💡</span>
                  营养建议
                </h3>
                <NutritionAdvice summary={todaySummary} profile={profile} />
              </div>
            )}

            {/* 用户档案卡片 - 移动端优化 */}
            <div
              className="dashboard-card bg-white/90 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-white/20 shadow-lg"
              onMouseEnter={handleCardHover}
              onMouseLeave={handleCardLeave}
            >
              <h3 className="text-lg sm:text-xl font-bold text-slate-800 mb-4 flex items-center gap-2">
                <span className="text-xl">👤</span>
                个人档案
              </h3>
              <div className="space-y-4">
                {/* 基本信息 - 移动端优化 */}
                <div className="grid grid-cols-2 gap-3 sm:gap-4">
                  <div className="stats shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50">
                    <div className="stat p-3 sm:p-4 text-center">
                      <div className="stat-figure text-blue-500 mb-1">
                        <span className="text-lg sm:text-xl">📏</span>
                      </div>
                      <div className="stat-value text-lg sm:text-xl text-blue-700">
                        {profile.height}
                      </div>
                      <div className="stat-desc text-xs text-blue-600">身高 (cm)</div>
                    </div>
                  </div>
                  <div className="stats shadow-sm bg-gradient-to-r from-green-50 to-emerald-50">
                    <div className="stat p-3 sm:p-4 text-center">
                      <div className="stat-figure text-green-500 mb-1">
                        <span className="text-lg sm:text-xl">⚖️</span>
                      </div>
                      <div className="stat-value text-lg sm:text-xl text-green-700">
                        {profile.weight}
                      </div>
                      <div className="stat-desc text-xs text-green-600">体重 (kg)</div>
                    </div>
                  </div>
                </div>

                {/* 目标信息 - 移动端优化 */}
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-3 sm:p-4 border border-purple-100">
                  <h4 className="font-bold text-purple-900 mb-3 flex items-center gap-2">
                    <span className="text-base sm:text-lg">🎯</span>
                    <span className="text-sm sm:text-base">减重目标</span>
                  </h4>
                  <div className="grid grid-cols-2 gap-2 sm:gap-3 text-xs sm:text-sm">
                    <div className="bg-white/60 rounded-lg p-2 sm:p-3">
                      <div className="text-purple-600 text-xs mb-1">目标体重</div>
                      <div className="font-bold text-purple-900 text-sm sm:text-base">{formatWeight(profile.targetWeight, 'kg')}</div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-2 sm:p-3">
                      <div className="text-purple-600 text-xs mb-1">需要减重</div>
                      <div className="font-bold text-purple-900 text-sm sm:text-base">{formatWeight(weightLoss, 'kg')}</div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-2 sm:p-3">
                      <div className="text-purple-600 text-xs mb-1">目标天数</div>
                      <div className="font-bold text-purple-900 text-sm sm:text-base">{profile.targetDays} 天</div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-2 sm:p-3">
                      <div className="text-purple-600 text-xs mb-1">每周减重</div>
                      <div className="font-bold text-purple-900 text-sm sm:text-base">{formatWeight(weeklyLoss, 'kg')}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 营养计划卡片 - 移动端优化 */}
            <div
              className="dashboard-card bg-white/90 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-white/20 shadow-lg"
              onMouseEnter={handleCardHover}
              onMouseLeave={handleCardLeave}
            >
              <h3 className="text-lg sm:text-xl font-bold text-slate-800 mb-4 flex items-center gap-2">
                <span className="text-xl">🍽️</span>
                营养计划
              </h3>
              <div className="space-y-4 sm:space-y-6">
                {/* 每日卡路里限额 - 移动端优化 */}
                <div className="stats shadow-lg bg-gradient-to-r from-green-50 to-emerald-50 w-full">
                  <div className="stat p-4 sm:p-6 text-center">
                    <div className="stat-figure text-green-500 mb-2">
                      <span className="text-2xl sm:text-3xl">🔥</span>
                    </div>
                    <div className="stat-value text-2xl sm:text-3xl text-green-700">
                      {formatCalories(profile.dailyCalorieLimit)}
                    </div>
                    <div className="stat-desc text-green-600 font-medium text-sm sm:text-base">每日卡路里限额</div>
                  </div>
                </div>

                {/* 三餐分配 - 移动端优化 */}
                <div className="space-y-3">
                  <h4 className="font-bold text-slate-800 flex items-center gap-2 text-base sm:text-lg">
                    <span className="text-lg">🍴</span>
                    三餐分配
                  </h4>
                  <div className="space-y-2 sm:space-y-3">
                    <div className="bg-white/60 rounded-lg p-2 sm:p-3 flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="text-base sm:text-lg">🌅</span>
                        <span className="font-medium text-slate-800 text-sm sm:text-base">早餐</span>
                      </div>
                      <div className="flex items-center gap-1 sm:gap-2">
                        <div className="badge badge-primary badge-sm sm:badge-md">
                          {Math.round(profile.mealRatios.breakfast * 100)}%
                        </div>
                        <span className="font-bold text-primary text-sm sm:text-base">
                          {formatCalories(profile.dailyCalorieLimit * profile.mealRatios.breakfast)}
                        </span>
                      </div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-2 sm:p-3 flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="text-base sm:text-lg">☀️</span>
                        <span className="font-medium text-slate-800 text-sm sm:text-base">午餐</span>
                      </div>
                      <div className="flex items-center gap-1 sm:gap-2">
                        <div className="badge badge-secondary badge-sm sm:badge-md">
                          {Math.round(profile.mealRatios.lunch * 100)}%
                        </div>
                        <span className="font-bold text-secondary text-sm sm:text-base">
                          {formatCalories(profile.dailyCalorieLimit * profile.mealRatios.lunch)}
                        </span>
                      </div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-2 sm:p-3 flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="text-base sm:text-lg">🌙</span>
                        <span className="font-medium text-slate-800 text-sm sm:text-base">晚餐</span>
                      </div>
                      <div className="flex items-center gap-1 sm:gap-2">
                        <div className="badge badge-accent badge-sm sm:badge-md">
                          {Math.round(profile.mealRatios.dinner * 100)}%
                        </div>
                        <span className="font-bold text-accent text-sm sm:text-base">
                          {formatCalories(profile.dailyCalorieLimit * profile.mealRatios.dinner)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 代谢信息 - 移动端优化 */}
                <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-3 sm:p-4 border border-orange-100">
                  <h4 className="font-bold text-orange-900 mb-3 flex items-center gap-2">
                    <span className="text-base sm:text-lg">⚡</span>
                    <span className="text-sm sm:text-base">代谢信息</span>
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <div className="stats shadow-sm bg-white/60">
                      <div className="stat p-3 sm:p-4 text-center">
                        <div className="stat-title text-xs text-orange-600">基础代谢率 (BMR)</div>
                        <div className="stat-value text-base sm:text-lg text-orange-700">{formatCalories(profile.bmr)}</div>
                      </div>
                    </div>
                    <div className="stats shadow-sm bg-white/60">
                      <div className="stat p-3 sm:p-4 text-center">
                        <div className="stat-title text-xs text-orange-600">总日消耗 (TDEE)</div>
                        <div className="stat-value text-base sm:text-lg text-orange-700">{formatCalories(profile.tdee)}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

        {/* 操作按钮 - 移动端优先设计 */}
        <div className="space-y-3 sm:space-y-4 mt-6 sm:mt-8">
          {/* 主要操作按钮 */}
          <button
            className="action-button btn btn-primary btn-lg w-full h-14 sm:h-16 shadow-lg"
            onClick={(e) => { handleButtonClick(e); alert('食物记录功能即将推出'); }}
          >
            <span className="text-xl sm:text-2xl mr-2 sm:mr-3">🍽️</span>
            <div className="text-left">
              <div className="font-bold text-sm sm:text-base">开始记录食物</div>
              <div className="text-xs opacity-80">记录今日饮食</div>
            </div>
          </button>

          {/* 次要操作按钮网格 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
            <button
              className="action-button btn btn-secondary btn-lg h-12 sm:h-14 shadow-lg"
              onClick={(e) => { handleButtonClick(e); addTestData(); }}
            >
              <span className="text-lg sm:text-xl mr-2">🧪</span>
              <div className="text-left">
                <div className="font-bold text-sm">添加测试数据</div>
                <div className="text-xs opacity-80">快速体验</div>
              </div>
            </button>
            <button
              className="action-button btn btn-accent btn-lg h-12 sm:h-14 shadow-lg"
              onClick={(e) => { handleButtonClick(e); navigate('/calendar'); }}
            >
              <span className="text-lg sm:text-xl mr-2">📅</span>
              <div className="text-left">
                <div className="font-bold text-sm">查看日历</div>
                <div className="text-xs opacity-80">历史记录</div>
              </div>
            </button>
          </div>

          {/* 测试功能按钮 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
            <button
              className="action-button btn btn-outline btn-lg h-12 sm:h-14 shadow-lg"
              onClick={(e) => { handleButtonClick(e); navigate('/camera-test'); }}
            >
              <span className="text-lg sm:text-xl mr-2">📷</span>
              <div className="text-left">
                <div className="font-bold text-sm">相机测试</div>
                <div className="text-xs opacity-80">拍照功能</div>
              </div>
            </button>
            <button
              className="action-button btn btn-outline btn-lg h-12 sm:h-14 shadow-lg"
              onClick={(e) => { handleButtonClick(e); navigate('/ai-test'); }}
            >
              <span className="text-lg sm:text-xl mr-2">🤖</span>
              <div className="text-left">
                <div className="font-bold text-sm">AI识别测试</div>
                <div className="text-xs opacity-80">智能识别</div>
              </div>
            </button>
          </div>

          {/* 重置按钮 */}
          <button
            className="action-button btn btn-ghost btn-lg w-full h-12 sm:h-14 shadow-lg border-2 border-dashed border-base-300"
            onClick={(e) => { handleButtonClick(e); clearProfile(); }}
          >
            <span className="text-lg sm:text-xl mr-2">🔄</span>
            <div className="text-left">
              <div className="font-bold text-sm">重新设置档案</div>
              <div className="text-xs opacity-80">重新开始</div>
            </div>
          </button>
        </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardPage;