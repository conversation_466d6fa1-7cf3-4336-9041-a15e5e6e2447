import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '@/domains/user/stores/userStore';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { NutritionCard, CalorieRing, NutritionAdvice } from '@/domains/nutrition';
import { Card, CardHeader, CardTitle, CardContent, Badge, Button, StaggerContainer, FadeIn } from '@/shared/components';
import { formatCalories, formatWeight, formatDate } from '@/shared/utils';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile, clearProfile } = useUserStore();
  const { getDailySummary, updateDailySummary } = useNutritionStore();

  const today = new Date();
  const todaySummary = getDailySummary(today);

  // 初始化今日营养数据
  useEffect(() => {
    if (profile && !todaySummary) {
      // 创建默认的每日汇总
      updateDailySummary(today, {
        totalCalories: 0,
        calorieLimit: profile.dailyCalorieLimit,
        remainingCalories: profile.dailyCalorieLimit,
        mealBreakdown: {
          breakfast: {
            mealType: 'breakfast',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.breakfast),
            foodCount: 0,
            percentage: 0
          },
          lunch: {
            mealType: 'lunch',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.lunch),
            foodCount: 0,
            percentage: 0
          },
          dinner: {
            mealType: 'dinner',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.dinner),
            foodCount: 0,
            percentage: 0
          },
          snack: {
            mealType: 'snack',
            calories: 0,
            calorieLimit: 0,
            foodCount: 0,
            percentage: 0
          }
        },
        nutrition: {
          protein: 0,
          fat: 0,
          carbs: 0,
          fiber: 0,
          sugar: 0
        },
        status: 'under',
        percentage: 0
      });
    }
  }, [profile, todaySummary, updateDailySummary, today]);

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            未找到用户档案
          </h2>
          <p className="text-gray-600">
            请先完成个人档案设置
          </p>
        </div>
      </div>
    );
  }

  const weightLoss = profile.weight - profile.targetWeight;
  const weeklyLoss = (weightLoss / profile.targetDays) * 7;

  // 模拟添加一些测试数据
  const addTestData = () => {
    if (todaySummary) {
      const testCalories = 650;
      updateDailySummary(today, {
        totalCalories: todaySummary.totalCalories + testCalories,
        mealBreakdown: {
          ...todaySummary.mealBreakdown,
          breakfast: {
            ...todaySummary.mealBreakdown.breakfast,
            calories: todaySummary.mealBreakdown.breakfast.calories + testCalories,
            foodCount: todaySummary.mealBreakdown.breakfast.foodCount + 1
          }
        },
        nutrition: {
          protein: todaySummary.nutrition.protein + 25,
          fat: todaySummary.nutrition.fat + 15,
          carbs: todaySummary.nutrition.carbs + 45,
          fiber: todaySummary.nutrition.fiber + 5,
          sugar: todaySummary.nutrition.sugar + 10
        }
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="container mx-auto max-w-7xl px-4 py-6 lg:px-8 lg:py-8">
        {/* 现代化欢迎信息 */}
        <FadeIn delay={100} className="text-center mb-8 lg:mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl mb-6">
            <span className="text-2xl">🎯</span>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-3">
            {profile.name ? `你好，${profile.name}` : '欢迎使用 KCal Tracker'}
          </h1>
          <p className="text-slate-600 lg:text-xl font-medium">
            {formatDate(today, 'yyyy年MM月dd日')} · 今天是您健康之旅的新一天
          </p>
          <div className="mt-4 inline-flex items-center gap-2 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-full border border-white/20">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-slate-700">系统运行正常</span>
          </div>
        </FadeIn>

        <StaggerContainer 
          className="space-y-6 lg:space-y-8"
          staggerDelay={120}
          animationDelay={300}
        >

        {/* 今日营养概览 - 现代化设计 */}
        {todaySummary && (
          <div data-stagger className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
            {/* 左侧：卡路里环形图 - 增强版 */}
            <div className="lg:col-span-1">
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-6 lg:p-8 border border-white/20 shadow-xl shadow-indigo-500/10">
                <div className="text-center mb-4">
                  <h3 className="text-lg font-bold text-slate-800 mb-1">今日卡路里</h3>
                  <p className="text-sm text-slate-600">距离目标还有 {todaySummary.calorieLimit - todaySummary.totalCalories} 卡</p>
                </div>
                <div className="flex justify-center">
                  <CalorieRing
                    current={todaySummary.totalCalories}
                    target={todaySummary.calorieLimit}
                    size="lg"
                  />
                </div>
                <div className="mt-4 grid grid-cols-2 gap-3">
                  <div className="text-center p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                    <div className="text-lg font-bold text-green-700">{todaySummary.totalCalories}</div>
                    <div className="text-xs text-green-600">已摄入</div>
                  </div>
                  <div className="text-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
                    <div className="text-lg font-bold text-blue-700">{todaySummary.calorieLimit}</div>
                    <div className="text-xs text-blue-600">目标</div>
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧：营养卡片 - 现代化 */}
            <div className="lg:col-span-2">
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-6 lg:p-8 border border-white/20 shadow-xl shadow-purple-500/10">
                <NutritionCard summary={todaySummary} />
              </div>
            </div>
          </div>
        )}

        {/* 第二行：营养建议和用户档案 - 现代化设计 */}
        <div data-stagger className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 mt-6 lg:mt-8">
          {/* 营养建议 - 现代化卡片 */}
          {todaySummary && (
            <div className="card bg-white/80 backdrop-blur-sm shadow-xl border border-white/20">
              <div className="card-body p-6">
                <h3 className="card-title text-xl mb-4 flex items-center gap-2">
                  <span className="text-2xl">💡</span>
                  营养建议
                </h3>
                <NutritionAdvice summary={todaySummary} profile={profile} />
              </div>
            </div>
          )}

          {/* 用户档案卡片 - 现代化设计 */}
          <div className="card bg-white/80 backdrop-blur-sm shadow-xl border border-white/20">
            <div className="card-body p-6">
              <h3 className="card-title text-xl mb-4 flex items-center gap-2">
                <span className="text-2xl">👤</span>
                个人档案
              </h3>
              <div className="space-y-4">
                {/* 基本信息 - 现代化stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="stats shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50">
                    <div className="stat p-4">
                      <div className="stat-figure text-blue-500">
                        <span className="text-2xl">📏</span>
                      </div>
                      <div className="stat-value text-2xl text-blue-700">
                        {profile.height}
                      </div>
                      <div className="stat-desc text-blue-600">身高 (cm)</div>
                    </div>
                  </div>
                  <div className="stats shadow-sm bg-gradient-to-r from-green-50 to-emerald-50">
                    <div className="stat p-4">
                      <div className="stat-figure text-green-500">
                        <span className="text-2xl">⚖️</span>
                      </div>
                      <div className="stat-value text-2xl text-green-700">
                        {profile.weight}
                      </div>
                      <div className="stat-desc text-green-600">体重 (kg)</div>
                    </div>
                  </div>
                </div>

                {/* 目标信息 - 现代化设计 */}
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-100">
                  <h4 className="font-bold text-purple-900 mb-3 flex items-center gap-2">
                    <span className="text-lg">🎯</span>
                    减重目标
                  </h4>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="text-purple-600 text-xs mb-1">目标体重</div>
                      <div className="font-bold text-purple-900">{formatWeight(profile.targetWeight, 'kg')}</div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="text-purple-600 text-xs mb-1">需要减重</div>
                      <div className="font-bold text-purple-900">{formatWeight(weightLoss, 'kg')}</div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="text-purple-600 text-xs mb-1">目标天数</div>
                      <div className="font-bold text-purple-900">{profile.targetDays} 天</div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="text-purple-600 text-xs mb-1">每周减重</div>
                      <div className="font-bold text-purple-900">{formatWeight(weeklyLoss, 'kg')}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 第三行：营养计划 - 现代化设计 */}
        <div data-stagger className="mt-6 lg:mt-8">
          <div className="card bg-white/80 backdrop-blur-sm shadow-xl border border-white/20">
            <div className="card-body p-6">
              <h3 className="card-title text-xl mb-6 flex items-center gap-2">
                <span className="text-2xl">🍽️</span>
                营养计划
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 每日卡路里限额 - 现代化设计 */}
                <div className="stats shadow-lg bg-gradient-to-r from-green-50 to-emerald-50">
                  <div className="stat p-6 text-center">
                    <div className="stat-figure text-green-500">
                      <span className="text-3xl">🔥</span>
                    </div>
                    <div className="stat-value text-3xl text-green-700">
                      {formatCalories(profile.dailyCalorieLimit)}
                    </div>
                    <div className="stat-desc text-green-600 font-medium">每日卡路里限额</div>
                  </div>
                </div>

                {/* 三餐分配 - 现代化设计 */}
                <div className="space-y-3">
                  <h4 className="font-bold text-base-content flex items-center gap-2">
                    <span className="text-lg">🍴</span>
                    三餐分配
                  </h4>
                  <div className="space-y-3">
                    <div className="bg-white/60 rounded-lg p-3 flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">🌅</span>
                        <span className="font-medium text-base-content">早餐</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="badge badge-primary">
                          {Math.round(profile.mealRatios.breakfast * 100)}%
                        </div>
                        <span className="font-bold text-primary">
                          {formatCalories(profile.dailyCalorieLimit * profile.mealRatios.breakfast)}
                        </span>
                      </div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3 flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">☀️</span>
                        <span className="font-medium text-base-content">午餐</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="badge badge-secondary">
                          {Math.round(profile.mealRatios.lunch * 100)}%
                        </div>
                        <span className="font-bold text-secondary">
                          {formatCalories(profile.dailyCalorieLimit * profile.mealRatios.lunch)}
                        </span>
                      </div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3 flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">🌙</span>
                        <span className="font-medium text-base-content">晚餐</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="badge badge-accent">
                          {Math.round(profile.mealRatios.dinner * 100)}%
                        </div>
                        <span className="font-bold text-accent">
                          {formatCalories(profile.dailyCalorieLimit * profile.mealRatios.dinner)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 代谢信息 - 现代化设计 */}
              <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-4 mt-6 border border-orange-100">
                <h4 className="font-bold text-orange-900 mb-3 flex items-center gap-2">
                  <span className="text-lg">⚡</span>
                  代谢信息
                </h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div className="stats shadow-sm bg-white/60">
                    <div className="stat p-4">
                      <div className="stat-title text-xs text-orange-600">基础代谢率 (BMR)</div>
                      <div className="stat-value text-lg text-orange-700">{formatCalories(profile.bmr)}</div>
                    </div>
                  </div>
                  <div className="stats shadow-sm bg-white/60">
                    <div className="stat p-4">
                      <div className="stat-title text-xs text-orange-600">总日消耗 (TDEE)</div>
                      <div className="stat-value text-lg text-orange-700">{formatCalories(profile.tdee)}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 第四行：操作按钮 - 现代化设计 */}
        <div data-stagger className="mt-6 lg:mt-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <button
              className="btn btn-primary btn-lg h-16 col-span-1 sm:col-span-2 lg:col-span-1 shadow-lg hover:shadow-xl transition-all duration-300"
              onClick={() => alert('食物记录功能即将推出')}
            >
              <span className="text-2xl mr-2">🍽️</span>
              <div className="text-left">
                <div className="font-bold">开始记录食物</div>
                <div className="text-xs opacity-80">记录今日饮食</div>
              </div>
            </button>
            <button
              className="btn btn-secondary btn-lg h-16 shadow-lg hover:shadow-xl transition-all duration-300"
              onClick={addTestData}
            >
              <span className="text-2xl mr-2">🧪</span>
              <div className="text-left">
                <div className="font-bold">添加测试数据</div>
                <div className="text-xs opacity-80">快速体验</div>
              </div>
            </button>
            <button
              className="btn btn-accent btn-lg h-16 shadow-lg hover:shadow-xl transition-all duration-300"
              onClick={() => navigate('/calendar')}
            >
              <span className="text-2xl mr-2">📅</span>
              <div className="text-left">
                <div className="font-bold">查看日历</div>
                <div className="text-xs opacity-80">历史记录</div>
              </div>
            </button>
            <button
              className="btn btn-outline btn-lg h-16 shadow-lg hover:shadow-xl transition-all duration-300"
              onClick={() => navigate('/camera-test')}
            >
              <span className="text-2xl mr-2">📷</span>
              <div className="text-left">
                <div className="font-bold">相机测试</div>
                <div className="text-xs opacity-80">拍照功能</div>
              </div>
            </button>
            <button
              className="btn btn-outline btn-lg h-16 shadow-lg hover:shadow-xl transition-all duration-300"
              onClick={() => navigate('/ai-test')}
            >
              <span className="text-2xl mr-2">🤖</span>
              <div className="text-left">
                <div className="font-bold">AI识别测试</div>
                <div className="text-xs opacity-80">智能识别</div>
              </div>
            </button>
            <button
              className="btn btn-ghost btn-lg h-16 shadow-lg hover:shadow-xl transition-all duration-300 border-2 border-dashed border-base-300"
              onClick={clearProfile}
            >
              <span className="text-2xl mr-2">🔄</span>
              <div className="text-left">
                <div className="font-bold">重新设置档案</div>
                <div className="text-xs opacity-80">重新开始</div>
              </div>
            </button>
          </div>
        </div>
        </StaggerContainer>
      </div>
    </div>
  );
};

export default DashboardPage;