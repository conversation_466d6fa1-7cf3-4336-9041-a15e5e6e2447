import React, { useState } from 'react';
import { Calendar, DateDetail, CalorieTrendChart, WeeklyComparisonChart } from '@/domains/analytics';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { Card, CardHeader, CardTitle, CardContent, Button } from '@/shared/components';
import { BottomNavigation } from '@/shared/components/navigation';
import { formatDate } from '@/shared/utils';
import { useNavigate } from 'react-router-dom';

const CalendarPage: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const { getDailySummary, getWeeklyAnalysis } = useNutritionStore();
  const navigate = useNavigate();

  const today = new Date();
  const selectedSummary = selectedDate ? getDailySummary(selectedDate) : null;
  const weeklyAnalysis = getWeeklyAnalysis(today);

  // 底部导航栏处理函数
  const handleRecordFood = () => {
    alert('食物记录功能即将推出');
  };

  const handleViewProfile = () => {
    navigate('/dashboard');
  };

  // 生成周度对比数据
  const weeklyComparisonData = weeklyAnalysis.trends.map((trend, index) => {
    const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const dayIndex = trend.date.getDay();
    
    return {
      day: dayNames[dayIndex],
      current: trend.calories,
      target: weeklyAnalysis.calories.target
    };
  });

  return (
    <div className="relative">
      <div
        className="min-h-screen bg-gray-50 p-4"
        style={{
          // 强制确保没有任何transform属性
          transform: 'none',
          // 避免创建层叠上下文
          isolation: 'auto',
          // 确保不影响fixed定位的子元素
          position: 'relative',
          zIndex: 'auto'
        }}
      >
        <div className="container mx-auto max-w-7xl">
        {/* 页面标题 - 移除动画组件 */}
        <div className="text-center mb-8">
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
            营养日历
          </h1>
          <p className="text-gray-600 lg:text-lg">
            查看您的营养摄入历史和趋势分析
          </p>
        </div>

        <div className="space-y-6 lg:space-y-8">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
            {/* 日历组件 */}
            <div className="xl:col-span-1">
              <Calendar
                selectedDate={selectedDate || undefined}
                onDateSelect={setSelectedDate}
              />
            </div>

            {/* 日期详情 */}
            <div className="xl:col-span-1">
              {selectedDate ? (
                <DateDetail
                  date={selectedDate}
                  summary={selectedSummary}
                  onClose={() => setSelectedDate(null)}
                />
              ) : (
                <Card>
                  <CardContent>
                    <div className="text-center py-12 text-gray-500">
                      <div className="text-4xl mb-4">📅</div>
                      <p className="text-lg font-medium mb-2">选择日期查看详情</p>
                      <p className="text-sm">点击日历上的任意日期查看当天的营养摄入详情</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* 趋势分析 */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
            {/* 卡路里趋势图 */}
            <Card>
              <CardHeader>
                <CardTitle>本周卡路里趋势</CardTitle>
              </CardHeader>
              <CardContent>
                <CalorieTrendChart data={weeklyAnalysis} height={250} />
              </CardContent>
            </Card>

            {/* 周度对比图 */}
            <Card>
              <CardHeader>
                <CardTitle>本周每日对比</CardTitle>
              </CardHeader>
              <CardContent>
                <WeeklyComparisonChart data={weeklyComparisonData} height={250} />
              </CardContent>
            </Card>
          </div>

          {/* 统计信息 */}
          <Card>
            <CardHeader>
              <CardTitle>本周统计</CardTitle>
            </CardHeader>
            <CardContent>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-700">
                  {Math.round(weeklyAnalysis.calories.average)}
                </div>
                <div className="text-sm text-blue-600">平均每日卡路里</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-700">
                  {Math.round(weeklyAnalysis.calories.total)}
                </div>
                <div className="text-sm text-green-600">本周总卡路里</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-700">
                  {Math.round(weeklyAnalysis.calories.adherenceRate * 100)}%
                </div>
                <div className="text-sm text-yellow-600">目标达成率</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-700">
                  {weeklyAnalysis.trends.filter(t => t.calories > 0).length}
                </div>
                <div className="text-sm text-purple-600">记录天数</div>
              </div>
            </div>
          </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button
              variant="primary"
              onClick={() => setSelectedDate(today)}
            >
              查看今天
            </Button>
            <Button
              variant="secondary"
              onClick={() => alert('导出功能即将推出')}
            >
              导出数据
            </Button>
          </div>
        </div>

        {/* 底部留白，避免内容被底部导航栏遮挡 */}
        <div className="h-24 pb-safe"></div>
        </div>
      </div>

      {/* 底部固定导航栏 - 移到根级别避免transform层叠上下文影响 */}
      <BottomNavigation
        onRecordFood={handleRecordFood}
        onViewProfile={handleViewProfile}
      />
    </div>
  );
};

export default CalendarPage;