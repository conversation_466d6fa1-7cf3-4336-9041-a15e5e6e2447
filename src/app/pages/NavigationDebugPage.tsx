import React, { useEffect, useState } from 'react';
import { BottomNavigation } from '@/shared/components/navigation';

const NavigationDebugPage: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const updateDebugInfo = () => {
      const nav = document.querySelector('.bottom-navigation') || document.querySelector('nav[style*="2147483647"]');
      const body = document.body;
      const html = document.documentElement;
      
      if (nav) {
        const computedStyle = window.getComputedStyle(nav);
        const rect = nav.getBoundingClientRect();
        
        setDebugInfo({
          // 基本信息
          exists: !!nav,
          tagName: nav.tagName,
          className: nav.className,
          
          // 位置信息
          position: computedStyle.position,
          top: computedStyle.top,
          bottom: computedStyle.bottom,
          left: computedStyle.left,
          right: computedStyle.right,
          zIndex: computedStyle.zIndex,
          
          // 显示信息
          display: computedStyle.display,
          visibility: computedStyle.visibility,
          opacity: computedStyle.opacity,
          
          // 尺寸信息
          width: computedStyle.width,
          height: computedStyle.height,
          minHeight: computedStyle.minHeight,
          
          // 边界信息
          rectTop: rect.top,
          rectBottom: rect.bottom,
          rectLeft: rect.left,
          rectRight: rect.right,
          rectWidth: rect.width,
          rectHeight: rect.height,
          
          // 视口信息
          viewportHeight: window.innerHeight,
          viewportWidth: window.innerWidth,
          
          // 滚动信息
          scrollY: window.scrollY,
          bodyScrollHeight: body.scrollHeight,
          bodyClientHeight: body.clientHeight,
          
          // 层叠上下文信息
          transform: computedStyle.transform,
          isolation: computedStyle.isolation,
          
          // 父元素信息
          parentTagName: nav.parentElement?.tagName,
          parentClassName: nav.parentElement?.className,
          parentPosition: nav.parentElement ? window.getComputedStyle(nav.parentElement).position : 'none',
          parentTransform: nav.parentElement ? window.getComputedStyle(nav.parentElement).transform : 'none',
          
          // 内联样式
          inlineStyle: (nav as HTMLElement).style.cssText,
          
          // 是否在视口内
          inViewport: rect.bottom >= 0 && rect.top <= window.innerHeight,
          
          // 是否被遮挡
          elementAtBottom: document.elementFromPoint(window.innerWidth / 2, window.innerHeight - 10)?.tagName,
        });
      } else {
        setDebugInfo({ exists: false, error: '找不到底部导航栏元素' });
      }
    };

    const handleScroll = () => {
      setScrollY(window.scrollY);
      updateDebugInfo();
    };

    updateDebugInfo();
    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', updateDebugInfo);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', updateDebugInfo);
    };
  }, []);

  const handleRecordFood = () => {
    alert('调试模式 - 记录食物');
  };

  const handleViewProfile = () => {
    alert('调试模式 - 查看档案');
  };

  return (
    <div className="relative">
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6 text-center">底部导航栏调试工具</h1>
          
          {/* 调试信息面板 */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">实时调试信息</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {Object.entries(debugInfo).map(([key, value]) => (
                <div key={key} className="flex justify-between border-b pb-1">
                  <span className="font-medium text-gray-600">{key}:</span>
                  <span className={`font-mono ${
                    key === 'exists' && !value ? 'text-red-600' :
                    key === 'position' && value !== 'fixed' ? 'text-red-600' :
                    key === 'zIndex' && value !== '2147483647' ? 'text-orange-600' :
                    key === 'inViewport' && !value ? 'text-red-600' :
                    'text-gray-900'
                  }`}>
                    {typeof value === 'boolean' ? (value ? '✅' : '❌') : String(value)}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* 滚动测试区域 */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">滚动测试区域</h2>
            <p className="mb-4">当前滚动位置: {scrollY}px</p>
            <div className="space-y-4">
              {Array.from({ length: 20 }, (_, i) => (
                <div key={i} className="bg-gray-50 p-4 rounded border">
                  <h3 className="font-medium">测试内容块 {i + 1}</h3>
                  <p className="text-gray-600">这是用于测试滚动的内容块。滚动页面时观察底部导航栏是否保持固定。</p>
                </div>
              ))}
            </div>
          </div>

          {/* 手动检测按钮 */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">手动检测工具</h2>
            <div className="space-x-4">
              <button
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                onClick={() => {
                  const nav = document.querySelector('.bottom-navigation');
                  if (nav) {
                    nav.scrollIntoView({ behavior: 'smooth', block: 'end' });
                  }
                }}
              >
                滚动到导航栏
              </button>
              <button
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
                onClick={() => {
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }}
              >
                滚动到顶部
              </button>
              <button
                className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
                onClick={() => {
                  window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                }}
              >
                滚动到底部
              </button>
            </div>
          </div>

          {/* 底部留白 */}
          <div className="h-32"></div>
        </div>
      </div>

      {/* 底部导航栏 */}
      <BottomNavigation
        onRecordFood={handleRecordFood}
        onViewProfile={handleViewProfile}
      />
    </div>
  );
};

export default NavigationDebugPage;
