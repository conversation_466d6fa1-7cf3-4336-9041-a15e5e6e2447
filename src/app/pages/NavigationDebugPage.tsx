import React, { useEffect, useState } from 'react';
import { BottomNavigation } from '@/shared/components/navigation';

interface ErrorReport {
  type: 'error' | 'warning' | 'info';
  message: string;
  solution: string;
  timestamp: number;
}

const NavigationDebugPage: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [scrollY, setScrollY] = useState(0);
  const [errors, setErrors] = useState<ErrorReport[]>([]);
  const [isFixed, setIsFixed] = useState(false);
  const [initialNavPosition, setInitialNavPosition] = useState<number | null>(null);

  // 添加错误报告
  const addError = (type: ErrorReport['type'], message: string, solution: string) => {
    const newError: ErrorReport = {
      type,
      message,
      solution,
      timestamp: Date.now()
    };
    setErrors(prev => [newError, ...prev.slice(0, 19)]); // 保留最新20条
  };

  // 检查导航栏是否真正固定
  const checkIfFixed = (nav: Element, rect: DOMRect) => {
    const currentBottom = rect.bottom;
    const viewportHeight = window.innerHeight;

    // 检查导航栏是否在视口底部
    const isAtBottom = Math.abs(currentBottom - viewportHeight) < 5;

    if (initialNavPosition === null) {
      setInitialNavPosition(currentBottom);
      return isAtBottom;
    }

    // 检查滚动时位置是否改变
    const positionChanged = Math.abs(currentBottom - initialNavPosition) > 5;
    const shouldBeFixed = !positionChanged && isAtBottom;

    return shouldBeFixed;
  };

  useEffect(() => {
    const updateDebugInfo = () => {
      const nav = document.querySelector('.bottom-navigation') || document.querySelector('nav[style*="2147483647"]');
      const body = document.body;
      const html = document.documentElement;

      // 清空之前的错误（每次检查时重新评估）
      setErrors([]);

      if (!nav) {
        addError('error', '底部导航栏元素不存在', '检查BottomNavigation组件是否正确渲染，确认选择器.bottom-navigation是否正确');
        setDebugInfo({ exists: false });
        return;
      }

      const computedStyle = window.getComputedStyle(nav);
      const rect = nav.getBoundingClientRect();
        
      // 自动错误检测
      const currentFixed = checkIfFixed(nav, rect);
      setIsFixed(currentFixed);

      // 1. 检查position属性
      if (computedStyle.position !== 'fixed') {
        addError('error', `position属性错误: ${computedStyle.position}`, '设置 position: fixed !important');
      }

      // 2. 检查bottom属性
      if (computedStyle.bottom !== '0px') {
        addError('error', `bottom属性错误: ${computedStyle.bottom}`, '设置 bottom: 0 !important');
      }

      // 3. 检查z-index
      const zIndex = parseInt(computedStyle.zIndex);
      if (isNaN(zIndex) || zIndex < 1000) {
        addError('warning', `z-index过低: ${computedStyle.zIndex}`, '设置更高的z-index值，建议使用2147483647');
      }

      // 4. 检查display和visibility
      if (computedStyle.display === 'none') {
        addError('error', 'display为none，导航栏不可见', '设置 display: block !important');
      }
      if (computedStyle.visibility === 'hidden') {
        addError('error', 'visibility为hidden，导航栏不可见', '设置 visibility: visible !important');
      }
      if (parseFloat(computedStyle.opacity) < 0.1) {
        addError('warning', `opacity过低: ${computedStyle.opacity}`, '设置 opacity: 1 !important');
      }

      // 5. 检查是否在视口内
      const inViewport = rect.bottom >= 0 && rect.top <= window.innerHeight;
      if (!inViewport) {
        addError('error', '导航栏不在视口内', '检查top/bottom值，确保导航栏在屏幕可见区域');
      }

      // 6. 检查是否被遮挡
      const elementAtBottom = document.elementFromPoint(window.innerWidth / 2, window.innerHeight - 10);
      if (elementAtBottom && !nav.contains(elementAtBottom)) {
        addError('warning', `导航栏被遮挡，底部元素: ${elementAtBottom.tagName}`, '检查z-index或其他元素的层级');
      }

      // 7. 检查父元素的transform
      let parent = nav.parentElement;
      while (parent && parent !== document.body) {
        const parentStyle = window.getComputedStyle(parent);
        if (parentStyle.transform !== 'none') {
          addError('error', `父元素${parent.tagName}有transform属性，创建了新的层叠上下文`, '将导航栏移到根级别，脱离transform影响');
          break;
        }
        parent = parent.parentElement;
      }

      // 8. 检查是否真正固定（滚动测试）
      if (window.scrollY > 100 && !currentFixed) {
        addError('error', '导航栏随页面滚动移动，未真正固定', '检查position:fixed是否生效，排查层叠上下文问题');
      }

      // 9. 检查尺寸
      if (rect.height < 50) {
        addError('warning', `导航栏高度过小: ${rect.height}px`, '设置合适的最小高度，建议72px以上');
      }

      setDebugInfo({
        // 基本信息
        exists: true,
        tagName: nav.tagName,
        className: nav.className,

        // 位置信息
        position: computedStyle.position,
        top: computedStyle.top,
        bottom: computedStyle.bottom,
        left: computedStyle.left,
        right: computedStyle.right,
        zIndex: computedStyle.zIndex,

        // 显示信息
        display: computedStyle.display,
        visibility: computedStyle.visibility,
        opacity: computedStyle.opacity,

        // 尺寸信息
        width: computedStyle.width,
        height: computedStyle.height,
        minHeight: computedStyle.minHeight,

        // 边界信息
        rectTop: rect.top,
        rectBottom: rect.bottom,
        rectLeft: rect.left,
        rectRight: rect.right,
        rectWidth: rect.width,
        rectHeight: rect.height,

        // 视口信息
        viewportHeight: window.innerHeight,
        viewportWidth: window.innerWidth,

        // 滚动信息
        scrollY: window.scrollY,
        bodyScrollHeight: body.scrollHeight,
        bodyClientHeight: body.clientHeight,

        // 层叠上下文信息
        transform: computedStyle.transform,
        isolation: computedStyle.isolation,

        // 父元素信息
        parentTagName: nav.parentElement?.tagName,
        parentClassName: nav.parentElement?.className,
        parentPosition: nav.parentElement ? window.getComputedStyle(nav.parentElement).position : 'none',
        parentTransform: nav.parentElement ? window.getComputedStyle(nav.parentElement).transform : 'none',

        // 内联样式
        inlineStyle: (nav as HTMLElement).style.cssText,

        // 是否在视口内
        inViewport,

        // 是否被遮挡
        elementAtBottom: elementAtBottom?.tagName,

        // 是否真正固定
        isFixed: currentFixed,
      });
    };

    const handleScroll = () => {
      setScrollY(window.scrollY);
      updateDebugInfo();
    };

    updateDebugInfo();
    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', updateDebugInfo);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', updateDebugInfo);
    };
  }, []);

  const handleRecordFood = () => {
    alert('调试模式 - 记录食物');
  };

  const handleViewProfile = () => {
    alert('调试模式 - 查看档案');
  };

  return (
    <div className="relative">
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6 text-center">
            底部导航栏固定性调试工具
            <div className={`text-sm mt-2 ${isFixed ? 'text-green-600' : 'text-red-600'}`}>
              状态: {isFixed ? '✅ 正常固定' : '❌ 未正确固定'}
            </div>
          </h1>

          {/* 错误报告面板 */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">
              自动错误检测
              <span className="text-sm text-gray-500">({errors.length} 个问题)</span>
            </h2>
            {errors.length === 0 ? (
              <div className="text-green-600 text-center py-4">
                ✅ 未检测到问题，导航栏应该正常固定
              </div>
            ) : (
              <div className="space-y-3">
                {errors.map((error, index) => (
                  <div key={index} className={`p-4 rounded-lg border-l-4 ${
                    error.type === 'error' ? 'bg-red-50 border-red-500' :
                    error.type === 'warning' ? 'bg-yellow-50 border-yellow-500' :
                    'bg-blue-50 border-blue-500'
                  }`}>
                    <div className={`font-medium ${
                      error.type === 'error' ? 'text-red-800' :
                      error.type === 'warning' ? 'text-yellow-800' :
                      'text-blue-800'
                    }`}>
                      {error.type === 'error' ? '🚨' : error.type === 'warning' ? '⚠️' : 'ℹ️'} {error.message}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      <strong>解决方案:</strong> {error.solution}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 详细调试信息面板 */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">详细技术信息</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {Object.entries(debugInfo).map(([key, value]) => (
                <div key={key} className="flex justify-between border-b pb-1">
                  <span className="font-medium text-gray-600">{key}:</span>
                  <span className={`font-mono ${
                    key === 'exists' && !value ? 'text-red-600' :
                    key === 'position' && value !== 'fixed' ? 'text-red-600' :
                    key === 'zIndex' && value !== '2147483647' ? 'text-orange-600' :
                    key === 'inViewport' && !value ? 'text-red-600' :
                    key === 'isFixed' && !value ? 'text-red-600' :
                    key === 'isFixed' && value ? 'text-green-600' :
                    'text-gray-900'
                  }`}>
                    {typeof value === 'boolean' ? (value ? '✅' : '❌') : String(value)}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* 固定性测试区域 */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">
              固定性测试区域
              <span className="text-sm text-gray-500">(滚动位置: {scrollY}px)</span>
            </h2>
            <div className="bg-blue-50 p-4 rounded-lg mb-4">
              <h3 className="font-medium text-blue-800 mb-2">测试说明:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• 滚动页面时，底部导航栏应该始终固定在屏幕底部</li>
                <li>• 如果导航栏随页面滚动移动，说明position:fixed未生效</li>
                <li>• 观察上方的错误报告，查看具体问题和解决方案</li>
              </ul>
            </div>
            <div className="space-y-4">
              {Array.from({ length: 30 }, (_, i) => (
                <div key={i} className="bg-gray-50 p-4 rounded border">
                  <h3 className="font-medium">测试内容块 {i + 1}</h3>
                  <p className="text-gray-600">
                    这是用于测试滚动的内容块。请滚动页面并观察底部导航栏是否保持固定在屏幕底部。
                    如果导航栏随页面移动，说明存在层叠上下文或定位问题。
                  </p>
                  {i === 10 && (
                    <div className="mt-2 p-2 bg-yellow-100 rounded text-yellow-800 text-sm">
                      ⚠️ 中间检查点：此时应该已经滚动了一定距离，导航栏应该仍然固定在底部
                    </div>
                  )}
                  {i === 20 && (
                    <div className="mt-2 p-2 bg-orange-100 rounded text-orange-800 text-sm">
                      🔍 深度测试点：大量滚动后，导航栏的固定性是关键测试指标
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 手动测试工具 */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">手动测试工具</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <button
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                onClick={() => {
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }}
              >
                📍 滚动到顶部
              </button>
              <button
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors"
                onClick={() => {
                  window.scrollTo({ top: window.innerHeight, behavior: 'smooth' });
                }}
              >
                📍 滚动一屏
              </button>
              <button
                className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-colors"
                onClick={() => {
                  window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                }}
              >
                📍 滚动到底部
              </button>
              <button
                className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
                onClick={() => {
                  // 强制重新检测
                  window.dispatchEvent(new Event('scroll'));
                }}
              >
                🔄 重新检测
              </button>
            </div>
            <div className="mt-4 p-3 bg-gray-50 rounded text-sm text-gray-600">
              💡 使用这些按钮测试不同滚动位置下导航栏的固定性。正常情况下，无论滚动到哪里，导航栏都应该保持在屏幕底部。
            </div>
          </div>

          {/* 底部留白 */}
          <div className="h-32"></div>
        </div>
      </div>

      {/* 底部导航栏 */}
      <BottomNavigation
        onRecordFood={handleRecordFood}
        onViewProfile={handleViewProfile}
      />
    </div>
  );
};

export default NavigationDebugPage;
