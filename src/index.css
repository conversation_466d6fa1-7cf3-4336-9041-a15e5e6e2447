@import "tailwindcss";
@plugin "daisyui";

/* 全局边框重置 - 修复Setup页面黑色边框问题 */
* {
  border: none !important;
  outline: none !important;
}

/* 恢复必要的边框样式 */
.input-bordered,
.btn-outline,
.card,
.alert,
.bottom-navigation {
  border: revert !important;
}

/* 2025年现代化配色主题 - 移动端优先 */
:root {
  /* 主色调：深蓝紫渐变系 */
  --color-primary: #6366f1; /* indigo-500 */
  --color-primary-focus: #4f46e5; /* indigo-600 */
  --color-primary-content: #ffffff;

  /* 次要色：紫粉渐变 */
  --color-secondary: #a855f7; /* purple-500 */
  --color-secondary-focus: #9333ea; /* purple-600 */
  --color-secondary-content: #ffffff;

  /* 强调色：青色 */
  --color-accent: #06b6d4; /* cyan-500 */
  --color-accent-focus: #0891b2; /* cyan-600 */
  --color-accent-content: #ffffff;

  /* 中性色：现代深灰 */
  --color-neutral: #1f2937; /* gray-800 */
  --color-neutral-focus: #111827; /* gray-900 */
  --color-neutral-content: #f9fafb; /* gray-50 */

  /* 基础色：柔和中性背景 */
  --color-base-100: #ffffff;
  --color-base-200: #f1f5f9; /* slate-100 */
  --color-base-300: #e2e8f0; /* slate-200 */
  --color-base-content: #0f172a; /* slate-900 */

  /* 状态色：2025年现代化 */
  --color-info: #3b82f6; /* blue-500 */
  --color-success: #10b981; /* emerald-500 */
  --color-warning: #f59e0b; /* amber-500 */
  --color-error: #ef4444; /* red-500 */

  /* 边框色：柔和中性系统 */
  --border-color: #e2e8f0; /* slate-200 */
  --border-color-hover: #cbd5e1; /* slate-300 */
  --border-color-focus: #6366f1; /* indigo-500 */
}

/* 2025年移动端优化边框系统 */
.input-bordered {
  border-color: var(--border-color) !important;
  border-width: 1.5px !important;
  transition: all 0.2s ease !important;
}

.input-bordered:hover {
  border-color: var(--border-color-hover) !important;
}

.input-bordered:focus {
  border-color: var(--border-color-focus) !important;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
  outline: none !important;
}

.btn {
  border-color: transparent !important;
  transition: all 0.2s ease !important;
}

.btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.btn-outline {
  border-color: var(--border-color-focus) !important;
  color: var(--border-color-focus) !important;
  background-color: transparent !important;
}

.btn-outline:hover {
  background-color: var(--border-color-focus) !important;
  border-color: var(--border-color-focus) !important;
  color: white !important;
}

/* 自定义颜色变量 - Tailwind CSS 4 兼容 */
:root {
  /* Primary colors (Green) */
  --color-primary-50: #f0fdf4;
  --color-primary-100: #dcfce7;
  --color-primary-200: #bbf7d0;
  --color-primary-300: #86efac;
  --color-primary-400: #4ade80;
  --color-primary-500: #22c55e;
  --color-primary-600: #16a34a;
  --color-primary-700: #15803d;
  --color-primary-800: #166534;
  --color-primary-900: #14532d;

  /* Danger colors (Red) */
  --color-danger-50: #fef2f2;
  --color-danger-100: #fee2e2;
  --color-danger-200: #fecaca;
  --color-danger-300: #fca5a5;
  --color-danger-400: #f87171;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  --color-danger-800: #991b1b;
  --color-danger-900: #7f1d1d;

  /* Warning colors (Yellow) */
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;
}

/* 移动端优化样式 */
@media (max-width: 640px) {
  /* 确保触控区域足够大 */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* 优化滚动性能 */
  .scroll-smooth {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 防止iOS缩放 */
  input[type="number"],
  input[type="text"],
  input[type="email"],
  input[type="password"] {
    font-size: 16px !important;
  }
}

/* 2024年现代化设计系统 */
:root {
  /* Glassmorphism 效果变量 */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(8px);

  /* 渐变色系 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

  /* 新的阴影系统 */
  --shadow-soft: 0 2px 20px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 30px rgba(0, 0, 0, 0.15);
  --shadow-strong: 0 8px 40px rgba(0, 0, 0, 0.2);
  --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
}

/* Glassmorphism 基础类 */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: 20px;
}

/* 现代化按钮样式 */
.modern-button {
  background: var(--gradient-primary);
  border: none;
  border-radius: 16px;
  color: white;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-medium);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-button:hover::before {
  left: 100%;
}

/* 支持深色模式的媒体查询 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f0f23;
    --text-primary: #f9fafb;
    --border-color: #374151;
    --glass-bg: rgba(0, 0, 0, 0.3);
    --glass-border: rgba(255, 255, 255, 0.1);
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@layer base {
  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    padding: 0;
    background-color: #f9fafb;
    color: #111827;
    min-height: 100vh;
    /* 移动端优化 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    /* 支持安全区域 */
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 移动端输入框优化 */
  input, textarea, select {
    font-size: 1rem;
    line-height: 1.5;
    -webkit-user-select: text;
    user-select: text;
  }

  /* 移动端按钮优化 */
  button, [role="button"] {
    -webkit-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }
}

@layer components {
  /* 自定义组件样式 */
  /* 移除自定义btn-primary和btn-secondary样式，使用DaisyUI原生样式 */

  .card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    padding: 1rem;
  }

  .input-field {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: border-color 0.2s, ring 0.2s;
  }
  
  .input-field:focus {
    outline: none;
    ring: 2px;
    ring-color: #22c55e;
    border-color: transparent;
  }

  /* DaisyUI风格的按钮组件 */
  /* 移除自定义btn样式，使用DaisyUI原生样式 */

  /* DaisyUI风格的表单组件 */
  .form-control {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.25rem 0;
  }

  .label-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: currentColor;
  }

  .label-text-alt {
    font-size: 0.75rem;
    color: currentColor;
    opacity: 0.7;
  }

  /* 移除自定义input样式，使用DaisyUI原生样式 */

  /* DaisyUI风格的卡片组件 */
  .card.glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .card-body {
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    padding: 2rem;
    gap: 0.5rem;
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
  }

  /* DaisyUI风格的导航栏 */
  .navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.5rem 1rem;
    min-height: 4rem;
  }

  .navbar-start {
    width: 50%;
    justify-content: flex-start;
    display: flex;
  }

  .navbar-center {
    flex-shrink: 0;
  }

  .navbar-end {
    width: 50%;
    justify-content: flex-end;
    display: flex;
  }

  /* 移除自定义steps样式，使用DaisyUI原生样式 */

  /* 移除自定义progress样式，使用DaisyUI原生样式 */

  /* DaisyUI风格的徽章 */
  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-duration: 200ms;
    height: 1.25rem;
    font-size: 0.75rem;
    line-height: 1rem;
    width: fit-content;
    padding-left: 0.563rem;
    padding-right: 0.563rem;
    border-radius: 1.9rem;
    border-width: 1px;
    border-color: transparent;
    background-color: #e5e7eb;
    color: #374151;
  }

  .badge-ghost {
    background-color: transparent;
    color: currentColor;
  }

  /* DaisyUI风格的统计组件 */
  .stat {
    display: inline-grid;
    width: 100%;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    grid-template-rows: repeat(3, minmax(0, 1fr));
    gap: 0.25rem;
    padding: 1rem;
  }

  .stat-title {
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 400;
  }

  .stat-value {
    font-size: 2rem;
    font-weight: 800;
    line-height: 1;
  }

  .stat-desc {
    color: #6b7280;
    font-size: 0.75rem;
  }

  /* DaisyUI风格的警告框 */
  .alert {
    display: grid;
    width: 100%;
    grid-auto-flow: row;
    align-content: flex-start;
    align-items: center;
    justify-items: center;
    gap: 1rem;
    text-align: center;
    border-radius: 0.5rem;
    border-width: 1px;
    border-color: transparent;
    padding: 1rem;
    grid-template-columns: auto minmax(0, 1fr);
    justify-items: start;
    text-align: start;
  }

  /* DaisyUI风格的加载动画 */
  .loading {
    pointer-events: none;
    display: inline-block;
    aspect-ratio: 1;
    width: 1.5rem;
    background-color: currentColor;
    mask-size: 100%;
    -webkit-mask-size: 100%;
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-position: center;
  }

  .loading-spinner {
    mask-image: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3e%3cstyle%3e.spinner_V8m1%7btransform-origin:center;animation:spinner_zKoa 2s linear infinite%7d.spinner_V8m1 circle%7bstroke-linecap:round;animation:spinner_YpZS 1.5s ease-in-out infinite%7d%40keyframes spinner_zKoa%7b100%25%7btransform:rotate(360deg)%7d%7d%40keyframes spinner_YpZS%7b0%25%7bstroke-dasharray:0 150;stroke-dashoffset:0%7d47.5%25%7bstroke-dasharray:42 150;stroke-dashoffset:-16%7d95%25%2c100%25%7bstroke-dasharray:42 150;stroke-dashoffset:-59%7d%7d%3c/style%3e%3cg class='spinner_V8m1'%3e%3ccircle cx='12' cy='12' r='9.5' fill='none' stroke='currentColor' stroke-width='3'%3e%3c/circle%3e%3c/g%3e%3c/svg%3e");
    -webkit-mask-image: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3e%3cstyle%3e.spinner_V8m1%7btransform-origin:center;animation:spinner_zKoa 2s linear infinite%7d.spinner_V8m1 circle%7bstroke-linecap:round;animation:spinner_YpZS 1.5s ease-in-out infinite%7d%40keyframes spinner_zKoa%7b100%25%7btransform:rotate(360deg)%7d%7d%40keyframes spinner_YpZS%7b0%25%7bstroke-dasharray:0 150;stroke-dashoffset:0%7d47.5%25%7bstroke-dasharray:42 150;stroke-dashoffset:-16%7d95%25%2c100%25%7bstroke-dasharray:42 150;stroke-dashoffset:-59%7d%7d%3c/style%3e%3cg class='spinner_V8m1'%3e%3ccircle cx='12' cy='12' r='9.5' fill='none' stroke='currentColor' stroke-width='3'%3e%3c/circle%3e%3c/g%3e%3c/svg%3e");
  }

  .loading-sm {
    width: 1rem;
  }
}

@layer utilities {
  /* 移动端工具类 */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 底部导航栏 - 基于Context7最佳实践，修复定位问题 */
  .bottom-navigation {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 9999 !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(12px) !important;
    -webkit-backdrop-filter: blur(12px) !important;
    border-top: 1px solid rgba(229, 231, 235, 0.5) !important;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1) !important;
    padding-bottom: env(safe-area-inset-bottom) !important;
    touch-action: manipulation !important;
    /* 确保不受父容器transform影响 */
    transform: none !important;
    will-change: auto !important;
  }

  /* 安全区域适配 */
  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .mb-safe {
    margin-bottom: env(safe-area-inset-bottom);
  }



  /* 加载动画 */
  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* 滚动条样式 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}