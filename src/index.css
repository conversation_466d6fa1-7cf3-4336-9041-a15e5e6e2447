@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    padding: 0;
    background-color: #f9fafb;
    color: #111827;
    min-height: 100vh;
    /* 移动端优化 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    /* 支持安全区域 */
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 移动端输入框优化 */
  input, textarea, select {
    font-size: 1rem;
    line-height: 1.5;
    -webkit-user-select: text;
    user-select: text;
  }

  /* 移动端按钮优化 */
  button, [role="button"] {
    -webkit-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }
}

@layer components {
  /* 自定义组件样式 */
  .btn-primary {
    background-color: #16a34a;
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
  }
  
  .btn-primary:hover {
    background-color: #15803d;
  }
  
  .btn-primary:focus {
    outline: none;
    ring: 2px;
    ring-color: #22c55e;
    ring-offset: 2px;
  }

  .btn-secondary {
    background-color: #e5e7eb;
    color: #111827;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
  }
  
  .btn-secondary:hover {
    background-color: #d1d5db;
  }
  
  .btn-secondary:focus {
    outline: none;
    ring: 2px;
    ring-color: #6b7280;
    ring-offset: 2px;
  }

  .card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    padding: 1rem;
  }

  .input-field {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: border-color 0.2s, ring 0.2s;
  }
  
  .input-field:focus {
    outline: none;
    ring: 2px;
    ring-color: #22c55e;
    border-color: transparent;
  }
}

@layer utilities {
  /* 移动端工具类 */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 加载动画 */
  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* 滚动条样式 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}