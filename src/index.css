@import "tailwindcss";

/* 自定义颜色变量 - Tailwind CSS 4 兼容 */
:root {
  /* Primary colors (Green) */
  --color-primary-50: #f0fdf4;
  --color-primary-100: #dcfce7;
  --color-primary-200: #bbf7d0;
  --color-primary-300: #86efac;
  --color-primary-400: #4ade80;
  --color-primary-500: #22c55e;
  --color-primary-600: #16a34a;
  --color-primary-700: #15803d;
  --color-primary-800: #166534;
  --color-primary-900: #14532d;

  /* Danger colors (Red) */
  --color-danger-50: #fef2f2;
  --color-danger-100: #fee2e2;
  --color-danger-200: #fecaca;
  --color-danger-300: #fca5a5;
  --color-danger-400: #f87171;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  --color-danger-800: #991b1b;
  --color-danger-900: #7f1d1d;

  /* Warning colors (Yellow) */
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;
}

/* 移动端优化样式 */
@media (max-width: 640px) {
  /* 确保触控区域足够大 */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* 优化滚动性能 */
  .scroll-smooth {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 防止iOS缩放 */
  input[type="number"],
  input[type="text"],
  input[type="email"],
  input[type="password"] {
    font-size: 16px !important;
  }
}

/* 2024年现代化设计系统 */
:root {
  /* Glassmorphism 效果变量 */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(8px);

  /* 渐变色系 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

  /* 新的阴影系统 */
  --shadow-soft: 0 2px 20px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 30px rgba(0, 0, 0, 0.15);
  --shadow-strong: 0 8px 40px rgba(0, 0, 0, 0.2);
  --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
}

/* Glassmorphism 基础类 */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: 20px;
}

/* 现代化按钮样式 */
.modern-button {
  background: var(--gradient-primary);
  border: none;
  border-radius: 16px;
  color: white;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-medium);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-button:hover::before {
  left: 100%;
}

/* 支持深色模式的媒体查询 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f0f23;
    --text-primary: #f9fafb;
    --border-color: #374151;
    --glass-bg: rgba(0, 0, 0, 0.3);
    --glass-border: rgba(255, 255, 255, 0.1);
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@layer base {
  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    padding: 0;
    background-color: #f9fafb;
    color: #111827;
    min-height: 100vh;
    /* 移动端优化 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    /* 支持安全区域 */
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 移动端输入框优化 */
  input, textarea, select {
    font-size: 1rem;
    line-height: 1.5;
    -webkit-user-select: text;
    user-select: text;
  }

  /* 移动端按钮优化 */
  button, [role="button"] {
    -webkit-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }
}

@layer components {
  /* 自定义组件样式 */
  .btn-primary {
    background-color: #16a34a;
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
  }
  
  .btn-primary:hover {
    background-color: #15803d;
  }
  
  .btn-primary:focus {
    outline: none;
    ring: 2px;
    ring-color: #22c55e;
    ring-offset: 2px;
  }

  .btn-secondary {
    background-color: #e5e7eb;
    color: #111827;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
  }
  
  .btn-secondary:hover {
    background-color: #d1d5db;
  }
  
  .btn-secondary:focus {
    outline: none;
    ring: 2px;
    ring-color: #6b7280;
    ring-offset: 2px;
  }

  .card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    padding: 1rem;
  }

  .input-field {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: border-color 0.2s, ring 0.2s;
  }
  
  .input-field:focus {
    outline: none;
    ring: 2px;
    ring-color: #22c55e;
    border-color: transparent;
  }
}

@layer utilities {
  /* 移动端工具类 */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 加载动画 */
  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* 滚动条样式 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}