import React, { useRef, useEffect } from 'react';
import { formatCalories, cn } from '@/shared/utils';
import { useAccessibleAnimation } from '@/shared/hooks/useAnimation';

interface CalorieRingProps {
  current: number;
  target: number;
  size?: 'sm' | 'md' | 'lg';
  showLabels?: boolean;
  className?: string;
}

const CalorieRing: React.FC<CalorieRingProps> = ({
  current,
  target,
  size = 'md',
  showLabels = true,
  className
}) => {
  const percentage = target > 0 ? (current / target) * 100 : 0;
  const clampedPercentage = Math.min(percentage, 100);
  const ringRef = useRef<HTMLDivElement>(null);
  const progressCircleRef = useRef<SVGCircleElement>(null);
  const { createAccessibleAnimation } = useAccessibleAnimation();
  
  // 尺寸配置
  const sizeConfig = {
    sm: {
      width: 80,
      height: 80,
      strokeWidth: 6,
      radius: 34,
      fontSize: 'text-xs',
      labelSize: 'text-xs'
    },
    md: {
      width: 120,
      height: 120,
      strokeWidth: 8,
      radius: 52,
      fontSize: 'text-sm',
      labelSize: 'text-sm'
    },
    lg: {
      width: 160,
      height: 160,
      strokeWidth: 10,
      radius: 70,
      fontSize: 'text-base',
      labelSize: 'text-base'
    }
  };

  const config = sizeConfig[size];
  const circumference = 2 * Math.PI * config.radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (clampedPercentage / 100) * circumference;

  // 根据百分比确定颜色
  const getColor = (percentage: number) => {
    if (percentage < 90) return '#3b82f6'; // blue-500
    if (percentage <= 110) return '#10b981'; // emerald-500
    if (percentage <= 130) return '#f59e0b'; // amber-500
    return '#ef4444'; // red-500
  };

  const color = getColor(percentage);
  const remaining = Math.max(0, target - current);
  
  // Animate ring on mount and value changes
  useEffect(() => {
    const progressCircle = progressCircleRef.current;
    if (!progressCircle) return;
    
    // Initial animation
    createAccessibleAnimation(progressCircle as unknown as HTMLElement, {
      strokeDashoffset: [circumference, strokeDashoffset],
      duration: 1000,
      easing: 'easeOutQuart',
      delay: 200
    });
  }, [current, target, createAccessibleAnimation, circumference, strokeDashoffset]);
  
  // Hover animation for the entire ring
  useEffect(() => {
    const ring = ringRef.current;
    if (!ring) return;
    
    const handleMouseEnter = () => {
      createAccessibleAnimation(ring, {
        scale: [1, 1.05],
        duration: 200,
        easing: 'easeOutQuart'
      });
    };
    
    const handleMouseLeave = () => {
      createAccessibleAnimation(ring, {
        scale: [1.05, 1],
        duration: 200,
        easing: 'easeOutQuart'
      });
    };
    
    ring.addEventListener('mouseenter', handleMouseEnter);
    ring.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      ring.removeEventListener('mouseenter', handleMouseEnter);
      ring.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [createAccessibleAnimation]);

  return (
    <div className={cn('flex flex-col items-center', className)}>
      {/* 环形进度条 */}
      <div 
        ref={ringRef}
        className="relative cursor-pointer transition-transform duration-200 hover:transform-gpu"
        role="progressbar"
        aria-valuenow={Math.round(clampedPercentage)}
        aria-valuemin={0}
        aria-valuemax={100}
        aria-label={`卡路里进度: ${Math.round(clampedPercentage)}%`}
      >
        <svg
          width={config.width}
          height={config.height}
          className="transform -rotate-90"
        >
          {/* 背景圆环 */}
          <circle
            cx={config.width / 2}
            cy={config.height / 2}
            r={config.radius}
            stroke="#e5e7eb"
            strokeWidth={config.strokeWidth}
            fill="none"
          />
          
          {/* 进度圆环 */}
          <circle
            ref={progressCircleRef}
            cx={config.width / 2}
            cy={config.height / 2}
            r={config.radius}
            stroke={color}
            strokeWidth={config.strokeWidth}
            fill="none"
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            className="transition-all duration-500 ease-out transform-gpu"
            style={{
              filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))'
            }}
          />
        </svg>
        
        {/* 中心文字 */}
        <div className="absolute inset-0 flex flex-col items-center justify-center transition-all duration-200">
          <div className={cn('font-bold text-gray-900 transition-colors duration-200', config.fontSize)}>
            {formatCalories(current)}
          </div>
          <div className={cn('text-gray-500 transition-all duration-200', config.fontSize === 'text-xs' ? 'text-xs' : 'text-xs')}>
            {Math.round(percentage)}%
          </div>
        </div>
      </div>

      {/* 标签信息 */}
      {showLabels && (
        <div className={cn('mt-2 text-center space-y-1 transition-all duration-200', config.labelSize)}>
          <div className="text-gray-600 transition-colors duration-200">
            目标：{formatCalories(target)}
          </div>
          <div className="text-gray-500 transition-colors duration-200">
            剩余：{formatCalories(remaining)}
          </div>
        </div>
      )}
    </div>
  );
};

export default CalorieRing;