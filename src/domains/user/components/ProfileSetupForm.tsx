import React, { useState } from 'react';
import { <PERSON><PERSON>, FormField, Card, CardHeader, CardTitle, CardContent, MobileNavBar } from '@/shared/components';
import { MobileStepIndicator, useMobileGestures } from '@/shared/components/molecules/MobileNavBar';
import { CreateUserProfileForm, Gender } from '@/shared/types';
import type { ActivityLevel } from '@/shared/types/user';
import { validateWeightLossGoal } from '@/shared/utils';
import { cn } from '@/shared/utils/format';

interface ProfileSetupFormProps {
  onSubmit: (data: CreateUserProfileForm) => Promise<void>;
  loading?: boolean;
}

const ProfileSetupForm: React.FC<ProfileSetupFormProps> = ({ onSubmit, loading = false }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<CreateUserProfileForm>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const totalSteps = 3;

  // 更新表单数据
  const updateFormData = (field: keyof CreateUserProfileForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 验证当前步骤
  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.height || formData.height < 100 || formData.height > 250) {
          newErrors.height = '请输入有效的身高 (100-250cm)';
        }
        if (!formData.weight || formData.weight < 30 || formData.weight > 300) {
          newErrors.weight = '请输入有效的体重 (30-300kg)';
        }
        if (!formData.age || formData.age < 10 || formData.age > 100) {
          newErrors.age = '请输入有效的年龄 (10-100岁)';
        }
        if (!formData.gender) {
          newErrors.gender = '请选择性别';
        }
        break;

      case 2:
        if (!formData.targetWeight || formData.targetWeight < 30 || formData.targetWeight > 300) {
          newErrors.targetWeight = '请输入有效的目标体重 (30-300kg)';
        }
        if (!formData.targetDays || formData.targetDays < 7 || formData.targetDays > 365) {
          newErrors.targetDays = '请输入有效的目标天数 (7-365天)';
        }
        if (!formData.activityLevel) {
          newErrors.activityLevel = '请选择活动水平';
        }

        // 验证减重目标安全性
        if (formData.weight && formData.targetWeight && formData.targetDays) {
          const validation = validateWeightLossGoal(
            formData.weight,
            formData.targetWeight,
            formData.targetDays
          );
          if (!validation.isValid) {
            newErrors.targetWeight = validation.message || '减重目标不安全';
          }
        }
        break;

      case 3:
        // 第三步是确认步骤，无需额外验证
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 下一步
  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  // 上一步
  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // 提交表单
  const handleSubmit = async () => {
    if (validateStep(currentStep)) {
      try {
        await onSubmit(formData as CreateUserProfileForm);
      } catch (error) {
        console.error('提交失败:', error);
      }
    }
  };

  // 活动水平选项
  const activityLevels: { value: ActivityLevel; label: string; description: string }[] = [
    { value: 'sedentary', label: '久坐不动', description: '很少或不运动' },
    { value: 'light', label: '轻度活动', description: '每周轻度运动1-3次' },
    { value: 'moderate', label: '中度活动', description: '每周中度运动3-5次' },
    { value: 'active', label: '高度活动', description: '每周高强度运动6-7次' },
    { value: 'veryActive', label: '极高活动', description: '每天高强度运动或体力劳动' }
  ];

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return '基本信息';
      case 2: return '目标设置';
      case 3: return '确认信息';
      default: return '设置个人档案';
    }
  };

  // 添加手势支持
  useMobileGestures(() => {
    if (currentStep > 1) {
      handlePrevious();
    }
  });

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* 2024年现代化背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-400 via-pink-500 to-red-500">
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
        {/* 动态背景元素 */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-40 right-10 w-24 h-24 bg-blue-300/20 rounded-full blur-lg animate-bounce"></div>
        <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-yellow-300/15 rounded-full blur-md animate-ping"></div>
      </div>

      {/* Glassmorphism 导航栏 */}
      <div className="relative z-10 glass-card mx-4 mt-4 p-4">
        <div className="flex items-center justify-between">
          {currentStep > 1 && (
            <button
              onClick={handlePrevious}
              className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}
          <h1 className="text-xl font-bold text-white text-center flex-1">
            {getStepTitle()}
          </h1>
          <div className="w-10"></div>
        </div>
      </div>

      {/* 现代化步骤指示器 */}
      <div className="relative z-10 mx-4 mt-6">
        <div className="glass-card p-4">
          <div className="flex items-center justify-between mb-3">
            {Array.from({ length: totalSteps }, (_, i) => (
              <div key={i} className="flex items-center">
                <div
                  className={cn(
                    'w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-500',
                    i + 1 <= currentStep
                      ? 'bg-gradient-to-r from-green-400 to-blue-500 text-white shadow-lg scale-110'
                      : 'bg-white/30 text-white/70'
                  )}
                >
                  {i + 1 <= currentStep ? (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    i + 1
                  )}
                </div>
                {i < totalSteps - 1 && (
                  <div className="flex-1 h-1 mx-3 bg-white/20 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-green-400 to-blue-500 transition-all duration-700 ease-out"
                      style={{ width: i + 1 < currentStep ? '100%' : '0%' }}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
          <div className="text-center text-white/80 text-sm">
            步骤 {currentStep} / {totalSteps}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 flex-1 px-4 py-6">
        <div className="glass-card p-8 space-y-8">
          {/* 第一步：基本信息 */}
          {currentStep === 1 && (
            <div className="space-y-8">
              {/* 现代化输入框 - 姓名 */}
              <div className="space-y-3">
                <label className="block text-white font-semibold text-lg">
                  👋 你的名字
                </label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="请输入你的名字（可选）"
                    value={formData.name || ''}
                    onChange={(e) => updateFormData('name', e.target.value)}
                    className="w-full px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-300"
                  />
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>

              {/* 身高体重网格 */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {/* 身高输入 */}
                <div className="space-y-3">
                  <label className="block text-white font-semibold text-lg">
                    📏 身高 <span className="text-red-300">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      placeholder="170"
                      value={formData.height || ''}
                      onChange={(e) => updateFormData('height', Number(e.target.value))}
                      className="w-full px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/40 transition-all duration-300"
                    />
                    <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 text-sm">cm</span>
                    {errors.height && (
                      <p className="mt-2 text-red-300 text-sm flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.height}
                      </p>
                    )}
                  </div>
                </div>

                {/* 体重输入 */}
                <div className="space-y-3">
                  <label className="block text-white font-semibold text-lg">
                    ⚖️ 体重 <span className="text-red-300">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      placeholder="65"
                      value={formData.weight || ''}
                      onChange={(e) => updateFormData('weight', Number(e.target.value))}
                      className="w-full px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-green-400/50 focus:border-green-400/40 transition-all duration-300"
                    />
                    <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 text-sm">kg</span>
                    {errors.weight && (
                      <p className="mt-2 text-red-300 text-sm flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.weight}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* 年龄输入 */}
              <div className="space-y-3">
                <label className="block text-white font-semibold text-lg">
                  🎂 年龄 <span className="text-red-300">*</span>
                </label>
                <div className="relative">
                  <input
                    type="number"
                    placeholder="25"
                    value={formData.age || ''}
                    onChange={(e) => updateFormData('age', Number(e.target.value))}
                    className="w-full px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400/50 focus:border-purple-400/40 transition-all duration-300"
                  />
                  <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 text-sm">岁</span>
                  {errors.age && (
                    <p className="mt-2 text-red-300 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.age}
                    </p>
                  )}
                </div>
              </div>

              {/* 性别选择 */}
              <div className="space-y-4">
                <label className="block text-white font-semibold text-lg">
                  👤 性别 <span className="text-red-300">*</span>
                </label>
                <div className="grid grid-cols-2 gap-4">
                  {(['male', 'female'] as Gender[]).map((gender) => (
                    <button
                      key={gender}
                      type="button"
                      onClick={() => updateFormData('gender', gender)}
                      className={cn(
                        'relative py-6 px-6 rounded-2xl text-center transition-all duration-300',
                        'min-h-[80px] font-bold text-lg overflow-hidden',
                        'touch-manipulation active:scale-95',
                        'border-2 backdrop-blur-sm',
                        formData.gender === gender
                          ? 'bg-gradient-to-r from-pink-500 to-violet-500 border-white/30 text-white shadow-2xl scale-105'
                          : 'bg-white/10 border-white/20 text-white/80 hover:bg-white/20 hover:border-white/30'
                      )}
                    >
                      <div className="relative z-10">
                        <div className="text-3xl mb-2">
                          {gender === 'male' ? '👨' : '👩'}
                        </div>
                        <div>{gender === 'male' ? '男性' : '女性'}</div>
                      </div>
                      {formData.gender === gender && (
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
                      )}
                    </button>
                  ))}
                </div>
                {errors.gender && (
                  <p className="mt-2 text-red-300 text-sm flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.gender}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* 第二步：目标设置 */}
          {currentStep === 2 && (
            <div className="space-y-6">
              {/* 移动端优化：垂直布局 */}
              <div className="space-y-4 sm:grid sm:grid-cols-2 sm:gap-4 sm:space-y-0">
                <FormField
                  label="目标体重"
                  type="number"
                  placeholder="60"
                  value={formData.targetWeight || ''}
                  onChange={(e) => updateFormData('targetWeight', Number(e.target.value))}
                  error={errors.targetWeight}
                  description="单位：千克(kg)"
                  required
                />
                <FormField
                  label="目标天数"
                  type="number"
                  placeholder="90"
                  value={formData.targetDays || ''}
                  onChange={(e) => updateFormData('targetDays', Number(e.target.value))}
                  error={errors.targetDays}
                  description="单位：天"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  活动水平 <span style={{ color: '#dc2626' }}>*</span>
                </label>
                <div className="space-y-3">
                  {activityLevels.map((level) => (
                    <button
                      key={level.value}
                      type="button"
                      onClick={() => updateFormData('activityLevel', level.value)}
                      className={cn(
                        'w-full p-4 rounded-xl border text-left transition-all duration-200',
                        'min-h-[64px] touch-manipulation active:scale-[0.98]',
                        formData.activityLevel === level.value
                          ? 'text-white shadow-sm'
                          : 'border-gray-300 hover:border-gray-400 bg-white'
                      )}
                      style={formData.activityLevel === level.value ? {
                        backgroundColor: '#16a34a',
                        borderColor: '#16a34a'
                      } : {}}
                    >
                      <div className="font-semibold text-base">{level.label}</div>
                      <div className={cn(
                        'text-sm mt-1',
                        formData.activityLevel === level.value ? 'text-green-100' : 'text-gray-500'
                      )}>
                        {level.description}
                      </div>
                    </button>
                  ))}
                </div>
                {errors.activityLevel && (
                  <p className="mt-2 text-sm" style={{ color: '#dc2626' }}>{errors.activityLevel}</p>
                )}
              </div>
            </div>
          )}

          {/* 第三步：确认信息 */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h4 className="font-medium text-gray-900">基本信息</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">身高：</span>
                    <span className="font-medium">{formData.height} cm</span>
                  </div>
                  <div>
                    <span className="text-gray-500">体重：</span>
                    <span className="font-medium">{formData.weight} kg</span>
                  </div>
                  <div>
                    <span className="text-gray-500">年龄：</span>
                    <span className="font-medium">{formData.age} 岁</span>
                  </div>
                  <div>
                    <span className="text-gray-500">性别：</span>
                    <span className="font-medium">{formData.gender === 'male' ? '男性' : '女性'}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h4 className="font-medium text-gray-900">目标设置</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">目标体重：</span>
                    <span className="font-medium">{formData.targetWeight} kg</span>
                  </div>
                  <div>
                    <span className="text-gray-500">目标天数：</span>
                    <span className="font-medium">{formData.targetDays} 天</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-500">活动水平：</span>
                    <span className="font-medium">
                      {activityLevels.find(l => l.value === formData.activityLevel)?.label}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-primary-50 rounded-lg p-4">
                <p className="text-sm text-primary-700">
                  点击"完成设置"后，系统将根据您的信息计算每日卡路里限额和营养建议。
                </p>
              </div>
            </div>
          )}

        </div>
      </div>

      {/* 现代化浮动按钮区域 */}
      <div className="relative z-20 p-6">
        <div className="flex gap-4">
          {currentStep > 1 && (
            <button
              onClick={handlePrevious}
              className="flex-1 py-4 px-6 bg-white/20 backdrop-blur-sm border border-white/30 rounded-2xl text-white font-semibold text-lg hover:bg-white/30 transition-all duration-300 active:scale-95"
            >
              ← 上一步
            </button>
          )}

          {currentStep < totalSteps ? (
            <button
              onClick={handleNext}
              disabled={loading}
              className="flex-1 py-4 px-6 bg-gradient-to-r from-green-400 to-blue-500 rounded-2xl text-white font-bold text-lg shadow-2xl hover:shadow-green-500/25 transition-all duration-300 active:scale-95 relative overflow-hidden group"
            >
              <span className="relative z-10">下一步 →</span>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="flex-1 py-4 px-6 bg-gradient-to-r from-pink-500 to-violet-600 rounded-2xl text-white font-bold text-lg shadow-2xl hover:shadow-pink-500/25 transition-all duration-300 active:scale-95 relative overflow-hidden group"
            >
              <span className="relative z-10 flex items-center justify-center">
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    设置中...
                  </>
                ) : (
                  '🎉 完成设置'
                )}
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileSetupForm;