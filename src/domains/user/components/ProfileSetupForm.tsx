import React, { useState, useRef } from 'react';
import { animate } from 'animejs';
import { CreateUserProfileForm, Gender, ActivityLevel } from '@/shared/types';
import { validateWeightLossGoal } from '@/shared/utils';

interface ProfileSetupFormProps {
  onSubmit: (data: CreateUserProfileForm) => Promise<void>;
  loading?: boolean;
}

const ProfileSetupForm: React.FC<ProfileSetupFormProps> = ({ onSubmit, loading = false }) => {
  // {{ AURA-X: 完全重新设计 - 清除所有遗留代码，全新DaisyUI布局. Approval: 寸止(ID:1705123500). }}
  const [step, setStep] = useState(1);
  const [data, setData] = useState<Partial<CreateUserProfileForm>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const containerRef = useRef<HTMLDivElement>(null);

  // 表单更新
  const updateField = (field: keyof CreateUserProfileForm, value: any) => {
    setData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 验证步骤
  const validateCurrentStep = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!data.height || data.height < 100 || data.height > 250) {
        newErrors.height = '身高需在100-250cm之间';
      }
      if (!data.weight || data.weight < 30 || data.weight > 300) {
        newErrors.weight = '体重需在30-300kg之间';
      }
      if (!data.age || data.age < 10 || data.age > 100) {
        newErrors.age = '年龄需在10-100岁之间';
      }
      if (!data.gender) {
        newErrors.gender = '请选择性别';
      }
    }

    if (step === 2) {
      if (!data.targetWeight || data.targetWeight < 30 || data.targetWeight > 300) {
        newErrors.targetWeight = '目标体重需在30-300kg之间';
      }
      if (!data.targetDays || data.targetDays < 7 || data.targetDays > 365) {
        newErrors.targetDays = '目标天数需在7-365天之间';
      }
      if (!data.activityLevel) {
        newErrors.activityLevel = '请选择活动水平';
      }

      if (data.weight && data.targetWeight && data.targetDays) {
        const validation = validateWeightLossGoal(data.weight, data.targetWeight, data.targetDays);
        if (!validation.isValid) {
          newErrors.targetWeight = validation.message || '减重目标不安全';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 步骤导航
  const nextStep = () => {
    if (validateCurrentStep()) {
      setStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    setStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (validateCurrentStep()) {
      await onSubmit(data as CreateUserProfileForm);
    }
  };

  const activityOptions = [
    { value: 'sedentary' as ActivityLevel, label: '久坐不动', desc: '很少或不运动', emoji: '🪑' },
    { value: 'light' as ActivityLevel, label: '轻度活动', desc: '每周轻度运动1-3次', emoji: '🚶' },
    { value: 'moderate' as ActivityLevel, label: '中度活动', desc: '每周中度运动3-5次', emoji: '🏃' },
    { value: 'active' as ActivityLevel, label: '高度活动', desc: '每周高强度运动6-7次', emoji: '🏋️' },
    { value: 'veryActive' as ActivityLevel, label: '极高活动', desc: '每天高强度运动或体力劳动', emoji: '⚡' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200 to-base-300" ref={containerRef}>
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        
        {/* 头部区域 */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-4">
            设置个人档案
          </h1>
          <p className="text-lg text-base-content/70">
            为您制定个性化的健康计划
          </p>
        </div>

        {/* 进度指示器 */}
        <div className="mb-8">
          <ul className="steps steps-horizontal w-full">
            <li className={`step ${step >= 1 ? 'step-primary' : ''}`} data-content={step > 1 ? '✓' : '1'}>
              基本信息
            </li>
            <li className={`step ${step >= 2 ? 'step-primary' : ''}`} data-content={step > 2 ? '✓' : '2'}>
              目标设置
            </li>
            <li className={`step ${step >= 3 ? 'step-primary' : ''}`} data-content={step > 3 ? '✓' : '3'}>
              确认信息
            </li>
          </ul>
        </div>

        {/* 主内容卡片 */}
        <div className="card bg-base-100 shadow-2xl">
          <div className="card-body p-8">

            {/* 步骤1：基本信息 */}
            {step === 1 && (
              <div className="space-y-8">
                <div className="text-center">
                  <div className="text-8xl mb-6">👋</div>
                  <h2 className="text-3xl font-bold mb-2">告诉我们关于你的信息</h2>
                  <p className="text-base-content/60">我们需要一些基本信息来为你定制计划</p>
                </div>

                <div className="space-y-6">
                  {/* 姓名 */}
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text text-lg font-medium">姓名（可选）</span>
                    </label>
                    <input
                      type="text"
                      placeholder="请输入您的姓名"
                      className="input input-bordered input-lg text-lg"
                      value={data.name || ''}
                      onChange={(e) => updateField('name', e.target.value)}
                    />
                  </div>

                  {/* 身高体重年龄网格 */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text text-lg font-medium">
                          身高 <span className="text-error">*</span>
                        </span>
                      </label>
                      <div className="join">
                        <input
                          type="number"
                          placeholder="170"
                          className={`input input-bordered input-lg join-item flex-1 text-lg ${errors.height ? 'input-error' : ''}`}
                          value={data.height || ''}
                          onChange={(e) => updateField('height', Number(e.target.value))}
                        />
                        <div className="btn btn-lg join-item no-animation">cm</div>
                      </div>
                      {errors.height && (
                        <label className="label">
                          <span className="label-text-alt text-error">{errors.height}</span>
                        </label>
                      )}
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text text-lg font-medium">
                          体重 <span className="text-error">*</span>
                        </span>
                      </label>
                      <div className="join">
                        <input
                          type="number"
                          placeholder="65"
                          className={`input input-bordered input-lg join-item flex-1 text-lg ${errors.weight ? 'input-error' : ''}`}
                          value={data.weight || ''}
                          onChange={(e) => updateField('weight', Number(e.target.value))}
                        />
                        <div className="btn btn-lg join-item no-animation">kg</div>
                      </div>
                      {errors.weight && (
                        <label className="label">
                          <span className="label-text-alt text-error">{errors.weight}</span>
                        </label>
                      )}
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text text-lg font-medium">
                          年龄 <span className="text-error">*</span>
                        </span>
                      </label>
                      <div className="join">
                        <input
                          type="number"
                          placeholder="25"
                          className={`input input-bordered input-lg join-item flex-1 text-lg ${errors.age ? 'input-error' : ''}`}
                          value={data.age || ''}
                          onChange={(e) => updateField('age', Number(e.target.value))}
                        />
                        <div className="btn btn-lg join-item no-animation">岁</div>
                      </div>
                      {errors.age && (
                        <label className="label">
                          <span className="label-text-alt text-error">{errors.age}</span>
                        </label>
                      )}
                    </div>
                  </div>

                  {/* 性别选择 */}
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text text-lg font-medium">
                        性别 <span className="text-error">*</span>
                      </span>
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      {(['male', 'female'] as Gender[]).map((gender) => (
                        <button
                          key={gender}
                          type="button"
                          onClick={() => updateField('gender', gender)}
                          className={`btn btn-lg h-24 ${
                            data.gender === gender ? 'btn-primary' : 'btn-outline'
                          }`}
                        >
                          <div className="flex flex-col items-center space-y-2">
                            <div className="text-4xl">
                              {gender === 'male' ? '👨' : '👩'}
                            </div>
                            <span className="text-lg font-bold">
                              {gender === 'male' ? '男性' : '女性'}
                            </span>
                          </div>
                        </button>
                      ))}
                    </div>
                    {errors.gender && (
                      <label className="label">
                        <span className="label-text-alt text-error">{errors.gender}</span>
                      </label>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 步骤2：目标设置 */}
            {step === 2 && (
              <div className="space-y-8">
                <div className="text-center">
                  <div className="text-8xl mb-6">🎯</div>
                  <h2 className="text-3xl font-bold mb-2">设定你的目标</h2>
                  <p className="text-base-content/60">告诉我们你想要达到什么目标</p>
                </div>

                <div className="space-y-6">
                  {/* 目标体重和天数 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text text-lg font-medium">
                          目标体重 <span className="text-error">*</span>
                        </span>
                      </label>
                      <div className="join">
                        <input
                          type="number"
                          placeholder="60"
                          className={`input input-bordered input-lg join-item flex-1 text-lg ${errors.targetWeight ? 'input-error' : ''}`}
                          value={data.targetWeight || ''}
                          onChange={(e) => updateField('targetWeight', Number(e.target.value))}
                        />
                        <div className="btn btn-lg join-item no-animation">kg</div>
                      </div>
                      {errors.targetWeight && (
                        <label className="label">
                          <span className="label-text-alt text-error">{errors.targetWeight}</span>
                        </label>
                      )}
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text text-lg font-medium">
                          目标天数 <span className="text-error">*</span>
                        </span>
                      </label>
                      <div className="join">
                        <input
                          type="number"
                          placeholder="90"
                          className={`input input-bordered input-lg join-item flex-1 text-lg ${errors.targetDays ? 'input-error' : ''}`}
                          value={data.targetDays || ''}
                          onChange={(e) => updateField('targetDays', Number(e.target.value))}
                        />
                        <div className="btn btn-lg join-item no-animation">天</div>
                      </div>
                      {errors.targetDays && (
                        <label className="label">
                          <span className="label-text-alt text-error">{errors.targetDays}</span>
                        </label>
                      )}
                    </div>
                  </div>

                  {/* 活动水平 */}
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text text-lg font-medium">
                        活动水平 <span className="text-error">*</span>
                      </span>
                    </label>
                    <div className="space-y-3">
                      {activityOptions.map((option) => (
                        <button
                          key={option.value}
                          type="button"
                          onClick={() => updateField('activityLevel', option.value)}
                          className={`btn btn-lg w-full justify-start h-auto p-6 ${
                            data.activityLevel === option.value ? 'btn-primary' : 'btn-outline'
                          }`}
                        >
                          <div className="flex items-center space-x-4 w-full">
                            <span className="text-3xl">{option.emoji}</span>
                            <div className="text-left">
                              <div className="text-lg font-bold">{option.label}</div>
                              <div className="text-sm opacity-70">{option.desc}</div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                    {errors.activityLevel && (
                      <label className="label">
                        <span className="label-text-alt text-error">{errors.activityLevel}</span>
                      </label>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 步骤3：确认信息 */}
            {step === 3 && (
              <div className="space-y-8">
                <div className="text-center">
                  <div className="text-8xl mb-6">✨</div>
                  <h2 className="text-3xl font-bold mb-2">确认你的信息</h2>
                  <p className="text-base-content/60">请确认以下信息无误</p>
                </div>

                <div className="space-y-6">
                  {/* 基本信息总结 */}
                  <div className="card bg-base-200">
                    <div className="card-body p-6">
                      <h3 className="card-title text-xl mb-4">👤 基本信息</h3>
                      <div className="stats stats-vertical lg:stats-horizontal shadow">
                        <div className="stat">
                          <div className="stat-title">姓名</div>
                          <div className="stat-value text-lg">{data.name || '未设置'}</div>
                        </div>
                        <div className="stat">
                          <div className="stat-title">性别</div>
                          <div className="stat-value text-lg">
                            {data.gender === 'male' ? '👨 男性' : '👩 女性'}
                          </div>
                        </div>
                        <div className="stat">
                          <div className="stat-title">年龄</div>
                          <div className="stat-value text-lg">{data.age} 岁</div>
                        </div>
                      </div>
                      <div className="stats stats-vertical lg:stats-horizontal shadow mt-4">
                        <div className="stat">
                          <div className="stat-title">身高</div>
                          <div className="stat-value text-lg">{data.height} cm</div>
                        </div>
                        <div className="stat">
                          <div className="stat-title">体重</div>
                          <div className="stat-value text-lg">{data.weight} kg</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 目标信息总结 */}
                  <div className="card bg-base-200">
                    <div className="card-body p-6">
                      <h3 className="card-title text-xl mb-4">🎯 目标设置</h3>
                      <div className="stats stats-vertical lg:stats-horizontal shadow">
                        <div className="stat">
                          <div className="stat-title">目标体重</div>
                          <div className="stat-value text-lg">{data.targetWeight} kg</div>
                        </div>
                        <div className="stat">
                          <div className="stat-title">目标天数</div>
                          <div className="stat-value text-lg">{data.targetDays} 天</div>
                        </div>
                      </div>
                      <div className="stats shadow mt-4">
                        <div className="stat">
                          <div className="stat-title">活动水平</div>
                          <div className="stat-value text-lg">
                            {activityOptions.find(opt => opt.value === data.activityLevel)?.emoji}{' '}
                            {activityOptions.find(opt => opt.value === data.activityLevel)?.label}
                          </div>
                          <div className="stat-desc">
                            {activityOptions.find(opt => opt.value === data.activityLevel)?.desc}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 准备完成提示 */}
                  <div className="alert alert-success">
                    <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <h3 className="font-bold">准备就绪！</h3>
                      <div className="text-xs">
                        点击完成按钮，系统将为您计算个性化的营养计划。
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-between items-center mt-8">
          <div className="flex-1">
            {step > 1 && (
              <button
                onClick={prevStep}
                className="btn btn-outline btn-lg"
                disabled={loading}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                上一步
              </button>
            )}
          </div>
          
          <div className="flex-1 flex justify-end">
            {step < 3 ? (
              <button
                onClick={nextStep}
                className="btn btn-primary btn-lg"
                disabled={loading}
              >
                下一步
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                className="btn btn-primary btn-lg"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    设置中...
                  </>
                ) : (
                  '🎉 完成设置'
                )}
              </button>
            )}
          </div>
        </div>

      </div>
    </div>
  );
};

export default ProfileSetupForm;