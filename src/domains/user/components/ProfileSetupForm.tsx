import React, { useState } from 'react';
import { <PERSON><PERSON>, FormField, Card, CardHeader, CardTitle, CardContent, MobileNavBar } from '@/shared/components';
import { MobileStepIndicator, useMobileGestures } from '@/shared/components/molecules/MobileNavBar';
import { CreateUserProfileForm, Gender } from '@/shared/types';
import type { ActivityLevel } from '@/shared/types/user';
import { validateWeightLossGoal } from '@/shared/utils';
import { cn } from '@/shared/utils/format';

interface ProfileSetupFormProps {
  onSubmit: (data: CreateUserProfileForm) => Promise<void>;
  loading?: boolean;
}

const ProfileSetupForm: React.FC<ProfileSetupFormProps> = ({ onSubmit, loading = false }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<CreateUserProfileForm>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const totalSteps = 3;

  // 更新表单数据
  const updateFormData = (field: keyof CreateUserProfileForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 验证当前步骤
  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.height || formData.height < 100 || formData.height > 250) {
          newErrors.height = '请输入有效的身高 (100-250cm)';
        }
        if (!formData.weight || formData.weight < 30 || formData.weight > 300) {
          newErrors.weight = '请输入有效的体重 (30-300kg)';
        }
        if (!formData.age || formData.age < 10 || formData.age > 100) {
          newErrors.age = '请输入有效的年龄 (10-100岁)';
        }
        if (!formData.gender) {
          newErrors.gender = '请选择性别';
        }
        break;

      case 2:
        if (!formData.targetWeight || formData.targetWeight < 30 || formData.targetWeight > 300) {
          newErrors.targetWeight = '请输入有效的目标体重 (30-300kg)';
        }
        if (!formData.targetDays || formData.targetDays < 7 || formData.targetDays > 365) {
          newErrors.targetDays = '请输入有效的目标天数 (7-365天)';
        }
        if (!formData.activityLevel) {
          newErrors.activityLevel = '请选择活动水平';
        }

        // 验证减重目标安全性
        if (formData.weight && formData.targetWeight && formData.targetDays) {
          const validation = validateWeightLossGoal(
            formData.weight,
            formData.targetWeight,
            formData.targetDays
          );
          if (!validation.isValid) {
            newErrors.targetWeight = validation.message || '减重目标不安全';
          }
        }
        break;

      case 3:
        // 第三步是确认步骤，无需额外验证
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 下一步
  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  // 上一步
  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // 提交表单
  const handleSubmit = async () => {
    if (validateStep(currentStep)) {
      try {
        await onSubmit(formData as CreateUserProfileForm);
      } catch (error) {
        console.error('提交失败:', error);
      }
    }
  };

  // 活动水平选项
  const activityLevels: { value: ActivityLevel; label: string; description: string }[] = [
    { value: 'sedentary', label: '久坐不动', description: '很少或不运动' },
    { value: 'light', label: '轻度活动', description: '每周轻度运动1-3次' },
    { value: 'moderate', label: '中度活动', description: '每周中度运动3-5次' },
    { value: 'active', label: '高度活动', description: '每周高强度运动6-7次' },
    { value: 'veryActive', label: '极高活动', description: '每天高强度运动或体力劳动' }
  ];

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return '基本信息';
      case 2: return '目标设置';
      case 3: return '确认信息';
      default: return '设置个人档案';
    }
  };

  // 添加手势支持
  useMobileGestures(() => {
    if (currentStep > 1) {
      handlePrevious();
    }
  });

  return (
    <div className="min-h-screen relative overflow-hidden" data-theme="kcal">
      {/* DaisyUI + 现代化背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary via-secondary to-accent">
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
        {/* 动态背景元素 */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-base-100/10 rounded-full blur-xl animate-float"></div>
        <div className="absolute bottom-40 right-10 w-24 h-24 bg-info/20 rounded-full blur-lg animate-bounce"></div>
        <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-warning/15 rounded-full blur-md animate-pulse-slow"></div>
      </div>

      {/* DaisyUI 导航栏 */}
      <div className="navbar relative z-10 glass-card mx-4 mt-4">
        <div className="navbar-start">
          {currentStep > 1 && (
            <button
              onClick={handlePrevious}
              className="btn btn-ghost btn-circle text-white hover:bg-white/20"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}
        </div>
        <div className="navbar-center">
          <h1 className="text-xl font-bold text-white">
            {getStepTitle()}
          </h1>
        </div>
        <div className="navbar-end">
          <div className="w-10"></div>
        </div>
      </div>

      {/* DaisyUI 步骤指示器 */}
      <div className="relative z-10 mx-4 mt-6">
        <div className="card glass">
          <div className="card-body p-4">
            <ul className="steps steps-horizontal w-full mb-4">
              {Array.from({ length: totalSteps }, (_, i) => (
                <li
                  key={i}
                  className={cn(
                    'step text-white',
                    i + 1 <= currentStep ? 'step-primary' : ''
                  )}
                  data-content={i + 1 <= currentStep ? '✓' : i + 1}
                >
                  {i === 0 && '基本信息'}
                  {i === 1 && '目标设置'}
                  {i === 2 && '确认信息'}
                </li>
              ))}
            </ul>
            <div className="text-center">
              <div className="badge badge-ghost text-white/80">
                步骤 {currentStep} / {totalSteps}
              </div>
            </div>
            <progress
              className="progress progress-primary w-full"
              value={currentStep}
              max={totalSteps}
            ></progress>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 flex-1 px-4 py-6">
        <div className="card glass">
          <div className="card-body space-y-8">
          {/* 第一步：基本信息 */}
          {currentStep === 1 && (
            <div className="space-y-8">
              {/* DaisyUI 输入框 - 姓名 */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text text-white font-semibold text-lg">
                    👋 你的名字
                  </span>
                </label>
                <input
                  type="text"
                  placeholder="请输入你的名字（可选）"
                  value={formData.name || ''}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  className="input input-bordered input-lg bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder-white/60 focus:border-primary focus:outline-none"
                />
              </div>

              {/* 身高体重网格 */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {/* 身高输入 */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text text-white font-semibold text-lg">
                      📏 身高 <span className="text-error">*</span>
                    </span>
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      placeholder="170"
                      value={formData.height || ''}
                      onChange={(e) => updateFormData('height', Number(e.target.value))}
                      className="input input-bordered input-lg bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder-white/60 focus:border-info focus:outline-none pr-12"
                    />
                    <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 text-sm">cm</span>
                  </div>
                  {errors.height && (
                    <label className="label">
                      <span className="label-text-alt text-error flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.height}
                      </span>
                    </label>
                  )}
                </div>

                {/* 体重输入 */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text text-white font-semibold text-lg">
                      ⚖️ 体重 <span className="text-error">*</span>
                    </span>
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      placeholder="65"
                      value={formData.weight || ''}
                      onChange={(e) => updateFormData('weight', Number(e.target.value))}
                      className="input input-bordered input-lg bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder-white/60 focus:border-success focus:outline-none pr-12"
                    />
                    <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 text-sm">kg</span>
                  </div>
                  {errors.weight && (
                    <label className="label">
                      <span className="label-text-alt text-error flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.weight}
                      </span>
                    </label>
                  )}
                </div>
              </div>

              {/* 年龄输入 */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text text-white font-semibold text-lg">
                    🎂 年龄 <span className="text-error">*</span>
                  </span>
                </label>
                <div className="relative">
                  <input
                    type="number"
                    placeholder="25"
                    value={formData.age || ''}
                    onChange={(e) => updateFormData('age', Number(e.target.value))}
                    className="input input-bordered input-lg bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder-white/60 focus:border-secondary focus:outline-none pr-12"
                  />
                  <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 text-sm">岁</span>
                </div>
                {errors.age && (
                  <label className="label">
                    <span className="label-text-alt text-error flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.age}
                    </span>
                  </label>
                )}
              </div>

              {/* 性别选择 */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text text-white font-semibold text-lg">
                    👤 性别 <span className="text-error">*</span>
                  </span>
                </label>
                <div className="grid grid-cols-2 gap-4">
                  {(['male', 'female'] as Gender[]).map((gender) => (
                    <button
                      key={gender}
                      type="button"
                      onClick={() => updateFormData('gender', gender)}
                      className={cn(
                        'btn btn-lg h-20 relative overflow-hidden transition-all duration-300',
                        'bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20',
                        formData.gender === gender
                          ? 'btn-primary scale-105 shadow-2xl animate-glow'
                          : 'hover:scale-105'
                      )}
                    >
                      <div className="flex flex-col items-center">
                        <div className="text-2xl mb-1">
                          {gender === 'male' ? '👨' : '👩'}
                        </div>
                        <div className="font-bold">
                          {gender === 'male' ? '男性' : '女性'}
                        </div>
                      </div>
                      {formData.gender === gender && (
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
                      )}
                    </button>
                  ))}
                </div>
                {errors.gender && (
                  <label className="label">
                    <span className="label-text-alt text-error flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.gender}
                    </span>
                  </label>
                )}
              </div>
            </div>
          )}

          {/* 第二步：目标设置 */}
          {currentStep === 2 && (
            <div className="space-y-6">
              {/* 移动端优化：垂直布局 */}
              <div className="space-y-4 sm:grid sm:grid-cols-2 sm:gap-4 sm:space-y-0">
                <FormField
                  label="目标体重"
                  type="number"
                  placeholder="60"
                  value={formData.targetWeight || ''}
                  onChange={(e) => updateFormData('targetWeight', Number(e.target.value))}
                  error={errors.targetWeight}
                  description="单位：千克(kg)"
                  required
                />
                <FormField
                  label="目标天数"
                  type="number"
                  placeholder="90"
                  value={formData.targetDays || ''}
                  onChange={(e) => updateFormData('targetDays', Number(e.target.value))}
                  error={errors.targetDays}
                  description="单位：天"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  活动水平 <span style={{ color: '#dc2626' }}>*</span>
                </label>
                <div className="space-y-3">
                  {activityLevels.map((level) => (
                    <button
                      key={level.value}
                      type="button"
                      onClick={() => updateFormData('activityLevel', level.value)}
                      className={cn(
                        'w-full p-4 rounded-xl border text-left transition-all duration-200',
                        'min-h-[64px] touch-manipulation active:scale-[0.98]',
                        formData.activityLevel === level.value
                          ? 'text-white shadow-sm'
                          : 'border-gray-300 hover:border-gray-400 bg-white'
                      )}
                      style={formData.activityLevel === level.value ? {
                        backgroundColor: '#16a34a',
                        borderColor: '#16a34a'
                      } : {}}
                    >
                      <div className="font-semibold text-base">{level.label}</div>
                      <div className={cn(
                        'text-sm mt-1',
                        formData.activityLevel === level.value ? 'text-green-100' : 'text-gray-500'
                      )}>
                        {level.description}
                      </div>
                    </button>
                  ))}
                </div>
                {errors.activityLevel && (
                  <p className="mt-2 text-sm" style={{ color: '#dc2626' }}>{errors.activityLevel}</p>
                )}
              </div>
            </div>
          )}

          {/* 第三步：确认信息 */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h4 className="font-medium text-gray-900">基本信息</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">身高：</span>
                    <span className="font-medium">{formData.height} cm</span>
                  </div>
                  <div>
                    <span className="text-gray-500">体重：</span>
                    <span className="font-medium">{formData.weight} kg</span>
                  </div>
                  <div>
                    <span className="text-gray-500">年龄：</span>
                    <span className="font-medium">{formData.age} 岁</span>
                  </div>
                  <div>
                    <span className="text-gray-500">性别：</span>
                    <span className="font-medium">{formData.gender === 'male' ? '男性' : '女性'}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h4 className="font-medium text-gray-900">目标设置</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">目标体重：</span>
                    <span className="font-medium">{formData.targetWeight} kg</span>
                  </div>
                  <div>
                    <span className="text-gray-500">目标天数：</span>
                    <span className="font-medium">{formData.targetDays} 天</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-500">活动水平：</span>
                    <span className="font-medium">
                      {activityLevels.find(l => l.value === formData.activityLevel)?.label}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-primary-50 rounded-lg p-4">
                <p className="text-sm text-primary-700">
                  点击"完成设置"后，系统将根据您的信息计算每日卡路里限额和营养建议。
                </p>
              </div>
            </div>
          )}

        </div>
      </div>

      {/* 现代化浮动按钮区域 */}
      <div className="relative z-20 p-6">
        <div className="flex gap-4">
          {currentStep > 1 && (
            <button
              onClick={handlePrevious}
              className="flex-1 py-4 px-6 bg-white/20 backdrop-blur-sm border border-white/30 rounded-2xl text-white font-semibold text-lg hover:bg-white/30 transition-all duration-300 active:scale-95"
            >
              ← 上一步
            </button>
          )}

          {currentStep < totalSteps ? (
            <button
              onClick={handleNext}
              disabled={loading}
              className="flex-1 py-4 px-6 bg-gradient-to-r from-green-400 to-blue-500 rounded-2xl text-white font-bold text-lg shadow-2xl hover:shadow-green-500/25 transition-all duration-300 active:scale-95 relative overflow-hidden group"
            >
              <span className="relative z-10">下一步 →</span>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="flex-1 py-4 px-6 bg-gradient-to-r from-pink-500 to-violet-600 rounded-2xl text-white font-bold text-lg shadow-2xl hover:shadow-pink-500/25 transition-all duration-300 active:scale-95 relative overflow-hidden group"
            >
              <span className="relative z-10 flex items-center justify-center">
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    设置中...
                  </>
                ) : (
                  '🎉 完成设置'
                )}
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileSetupForm;