import React, { useState } from 'react';
import { <PERSON><PERSON>, FormField, Card, CardHeader, CardTitle, CardContent, MobileNavBar } from '@/shared/components';
import { MobileStepIndicator } from '@/shared/components/molecules/MobileNavBar';
import { CreateUserProfileForm, Gender } from '@/shared/types';
import type { ActivityLevel } from '@/shared/types/user';
import { validateWeightLossGoal } from '@/shared/utils';
import { cn } from '@/shared/utils/format';

interface ProfileSetupFormProps {
  onSubmit: (data: CreateUserProfileForm) => Promise<void>;
  loading?: boolean;
}

const ProfileSetupForm: React.FC<ProfileSetupFormProps> = ({ onSubmit, loading = false }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<CreateUserProfileForm>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const totalSteps = 3;

  // 更新表单数据
  const updateFormData = (field: keyof CreateUserProfileForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 验证当前步骤
  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.height || formData.height < 100 || formData.height > 250) {
          newErrors.height = '请输入有效的身高 (100-250cm)';
        }
        if (!formData.weight || formData.weight < 30 || formData.weight > 300) {
          newErrors.weight = '请输入有效的体重 (30-300kg)';
        }
        if (!formData.age || formData.age < 10 || formData.age > 100) {
          newErrors.age = '请输入有效的年龄 (10-100岁)';
        }
        if (!formData.gender) {
          newErrors.gender = '请选择性别';
        }
        break;

      case 2:
        if (!formData.targetWeight || formData.targetWeight < 30 || formData.targetWeight > 300) {
          newErrors.targetWeight = '请输入有效的目标体重 (30-300kg)';
        }
        if (!formData.targetDays || formData.targetDays < 7 || formData.targetDays > 365) {
          newErrors.targetDays = '请输入有效的目标天数 (7-365天)';
        }
        if (!formData.activityLevel) {
          newErrors.activityLevel = '请选择活动水平';
        }

        // 验证减重目标安全性
        if (formData.weight && formData.targetWeight && formData.targetDays) {
          const validation = validateWeightLossGoal(
            formData.weight,
            formData.targetWeight,
            formData.targetDays
          );
          if (!validation.isValid) {
            newErrors.targetWeight = validation.message || '减重目标不安全';
          }
        }
        break;

      case 3:
        // 第三步是确认步骤，无需额外验证
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 下一步
  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  // 上一步
  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // 提交表单
  const handleSubmit = async () => {
    if (validateStep(currentStep)) {
      try {
        await onSubmit(formData as CreateUserProfileForm);
      } catch (error) {
        console.error('提交失败:', error);
      }
    }
  };

  // 活动水平选项
  const activityLevels: { value: ActivityLevel; label: string; description: string }[] = [
    { value: 'sedentary', label: '久坐不动', description: '很少或不运动' },
    { value: 'light', label: '轻度活动', description: '每周轻度运动1-3次' },
    { value: 'moderate', label: '中度活动', description: '每周中度运动3-5次' },
    { value: 'active', label: '高度活动', description: '每周高强度运动6-7次' },
    { value: 'veryActive', label: '极高活动', description: '每天高强度运动或体力劳动' }
  ];

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return '基本信息';
      case 2: return '目标设置';
      case 3: return '确认信息';
      default: return '设置个人档案';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 移动端导航栏 */}
      <MobileNavBar
        title={getStepTitle()}
        showBack={currentStep > 1}
        onBack={handlePrevious}
      />

      {/* 移动端步骤指示器 */}
      <MobileStepIndicator
        currentStep={currentStep}
        totalSteps={totalSteps}
        className="bg-white border-b border-gray-200"
      />

      {/* 主要内容区域 */}
      <div className="flex-1 px-4 py-6 space-y-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          {/* 第一步：基本信息 */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <FormField
                label="姓名"
                placeholder="请输入姓名（可选）"
                value={formData.name || ''}
                onChange={(e) => updateFormData('name', e.target.value)}
              />

              {/* 移动端优化：垂直布局 */}
              <div className="space-y-4 sm:grid sm:grid-cols-2 sm:gap-4 sm:space-y-0">
                <FormField
                  label="身高"
                  type="number"
                  placeholder="170"
                  value={formData.height || ''}
                  onChange={(e) => updateFormData('height', Number(e.target.value))}
                  error={errors.height}
                  description="单位：厘米(cm)"
                  required
                />
                <FormField
                  label="体重"
                  type="number"
                  placeholder="65"
                  value={formData.weight || ''}
                  onChange={(e) => updateFormData('weight', Number(e.target.value))}
                  error={errors.weight}
                  description="单位：千克(kg)"
                  required
                />
              </div>

              <FormField
                label="年龄"
                type="number"
                placeholder="25"
                value={formData.age || ''}
                onChange={(e) => updateFormData('age', Number(e.target.value))}
                error={errors.age}
                description="单位：岁"
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  性别 <span style={{ color: '#dc2626' }}>*</span>
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {(['male', 'female'] as Gender[]).map((gender) => (
                    <button
                      key={gender}
                      type="button"
                      onClick={() => updateFormData('gender', gender)}
                      className={cn(
                        'py-4 px-6 rounded-xl border text-center transition-all duration-200',
                        'min-h-[56px] font-medium text-base',
                        'touch-manipulation active:scale-95',
                        formData.gender === gender
                          ? 'text-white shadow-sm'
                          : 'border-gray-300 hover:border-gray-400 bg-white text-gray-700'
                      )}
                      style={formData.gender === gender ? {
                        backgroundColor: '#16a34a',
                        borderColor: '#16a34a'
                      } : {}}
                    >
                      {gender === 'male' ? '男性' : '女性'}
                    </button>
                  ))}
                </div>
                {errors.gender && (
                  <p className="mt-2 text-sm" style={{ color: '#dc2626' }}>{errors.gender}</p>
                )}
              </div>
            </div>
          )}

          {/* 第二步：目标设置 */}
          {currentStep === 2 && (
            <div className="space-y-6">
              {/* 移动端优化：垂直布局 */}
              <div className="space-y-4 sm:grid sm:grid-cols-2 sm:gap-4 sm:space-y-0">
                <FormField
                  label="目标体重"
                  type="number"
                  placeholder="60"
                  value={formData.targetWeight || ''}
                  onChange={(e) => updateFormData('targetWeight', Number(e.target.value))}
                  error={errors.targetWeight}
                  description="单位：千克(kg)"
                  required
                />
                <FormField
                  label="目标天数"
                  type="number"
                  placeholder="90"
                  value={formData.targetDays || ''}
                  onChange={(e) => updateFormData('targetDays', Number(e.target.value))}
                  error={errors.targetDays}
                  description="单位：天"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  活动水平 <span style={{ color: '#dc2626' }}>*</span>
                </label>
                <div className="space-y-3">
                  {activityLevels.map((level) => (
                    <button
                      key={level.value}
                      type="button"
                      onClick={() => updateFormData('activityLevel', level.value)}
                      className={cn(
                        'w-full p-4 rounded-xl border text-left transition-all duration-200',
                        'min-h-[64px] touch-manipulation active:scale-[0.98]',
                        formData.activityLevel === level.value
                          ? 'text-white shadow-sm'
                          : 'border-gray-300 hover:border-gray-400 bg-white'
                      )}
                      style={formData.activityLevel === level.value ? {
                        backgroundColor: '#16a34a',
                        borderColor: '#16a34a'
                      } : {}}
                    >
                      <div className="font-semibold text-base">{level.label}</div>
                      <div className={cn(
                        'text-sm mt-1',
                        formData.activityLevel === level.value ? 'text-green-100' : 'text-gray-500'
                      )}>
                        {level.description}
                      </div>
                    </button>
                  ))}
                </div>
                {errors.activityLevel && (
                  <p className="mt-2 text-sm" style={{ color: '#dc2626' }}>{errors.activityLevel}</p>
                )}
              </div>
            </div>
          )}

          {/* 第三步：确认信息 */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h4 className="font-medium text-gray-900">基本信息</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">身高：</span>
                    <span className="font-medium">{formData.height} cm</span>
                  </div>
                  <div>
                    <span className="text-gray-500">体重：</span>
                    <span className="font-medium">{formData.weight} kg</span>
                  </div>
                  <div>
                    <span className="text-gray-500">年龄：</span>
                    <span className="font-medium">{formData.age} 岁</span>
                  </div>
                  <div>
                    <span className="text-gray-500">性别：</span>
                    <span className="font-medium">{formData.gender === 'male' ? '男性' : '女性'}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h4 className="font-medium text-gray-900">目标设置</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">目标体重：</span>
                    <span className="font-medium">{formData.targetWeight} kg</span>
                  </div>
                  <div>
                    <span className="text-gray-500">目标天数：</span>
                    <span className="font-medium">{formData.targetDays} 天</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-500">活动水平：</span>
                    <span className="font-medium">
                      {activityLevels.find(l => l.value === formData.activityLevel)?.label}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-primary-50 rounded-lg p-4">
                <p className="text-sm text-primary-700">
                  点击"完成设置"后，系统将根据您的信息计算每日卡路里限额和营养建议。
                </p>
              </div>
            </div>
          )}

        </div>
      </div>

      {/* 固定底部按钮区域 */}
      <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4 safe-area-inset">
        <div className="flex gap-3">
          {currentStep > 1 && (
            <Button
              variant="secondary"
              onClick={handlePrevious}
              className="flex-1"
              size="lg"
            >
              上一步
            </Button>
          )}

          {currentStep < totalSteps ? (
            <Button
              variant="primary"
              onClick={handleNext}
              className="flex-1"
              size="lg"
            >
              下一步
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={handleSubmit}
              loading={loading}
              className="flex-1"
              size="lg"
            >
              完成设置
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileSetupForm;